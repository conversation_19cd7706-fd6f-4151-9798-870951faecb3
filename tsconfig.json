{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@core": ["./core"], "@core/*": ["./core/*"], "@database": ["./core/database"], "@database/*": ["./core/database/*"], "@ui": ["./ui"], "@ui/*": ["./ui/*"], "@utils": ["./core/utils"], "@utils/*": ["./core/utils/*"], "@config": ["./core/config"], "@component": ["./ui/components"], "@component/*": ["./ui/components/*"]}, "types": ["./cloudflare-env.d.ts", "node"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}