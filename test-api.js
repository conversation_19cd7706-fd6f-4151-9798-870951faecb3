// Simple test script to verify API endpoints work
// Run with: node test-api.js

const BASE_URL = 'http://localhost:3000';

async function testHealthEndpoint() {
  try {
    console.log('Testing health endpoint...');
    const response = await fetch(`${BASE_URL}/api/health`);
    const data = await response.json();
    
    console.log('Health check response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Health endpoint working correctly');
    } else {
      console.log('❌ Health endpoint returned error');
    }
  } catch (error) {
    console.error('❌ Health endpoint failed:', error.message);
  }
}

async function testAuthEndpoint() {
  try {
    console.log('\nTesting auth endpoint...');
    const response = await fetch(`${BASE_URL}/api/auth/session`);
    
    console.log('Auth endpoint status:', response.status);
    
    if (response.status === 401 || response.status === 200) {
      console.log('✅ Auth endpoint responding correctly');
    } else {
      console.log('❌ Auth endpoint unexpected status');
    }
  } catch (error) {
    console.error('❌ Auth endpoint failed:', error.message);
  }
}

async function testProtectedEndpoint() {
  try {
    console.log('\nTesting protected organizations endpoint...');
    const response = await fetch(`${BASE_URL}/~/api/organizations`);
    const data = await response.json();
    
    console.log('Protected endpoint response:', JSON.stringify(data, null, 2));
    
    if (response.status === 401 && data.error?.code === 'UNAUTHORIZED') {
      console.log('✅ Protected endpoint correctly requires authentication');
    } else {
      console.log('❌ Protected endpoint not properly secured');
    }
  } catch (error) {
    console.error('❌ Protected endpoint failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting API tests...\n');
  
  await testHealthEndpoint();
  await testAuthEndpoint();
  await testProtectedEndpoint();
  
  console.log('\n✨ Tests completed!');
}

runTests().catch(console.error);
