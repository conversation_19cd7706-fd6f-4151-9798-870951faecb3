import type { NextRequest } from "next/server";
import { apiInternalError, apiSuccess } from "@/core/api/response";
import { getConfigs } from "@/core/config";
import { getDb } from "@/core/database";

export async function GET(_request: NextRequest) {
	try {
		const _config = getConfigs();

		// Check database connection
		const db = getDb();
		const dbStart = Date.now();

		try {
			await db.execute("SELECT 1");
			const dbTime = Date.now() - dbStart;

			const services = {
				database: dbTime < 1000 ? "connected" : "slow",
				auth: "operational",
				api: "operational",
			};

			const overallStatus = Object.values(services).every((status) =>
				["connected", "operational"].includes(status),
			)
				? "healthy"
				: "degraded";

			return apiSuccess({
				status: overallStatus,
				timestamp: new Date().toISOString(),
				version: "1.0.0",
				environment: process.env.NODE_ENV || "development",
				services,
				metrics: {
					databaseResponseTime: dbTime,
				},
			});
		} catch (dbError) {
			console.error("Database health check failed:", dbError);

			const services = {
				database: "disconnected",
				auth: "operational",
				api: "operational",
			};

			return apiSuccess({
				status: "unhealthy",
				timestamp: new Date().toISOString(),
				version: "1.0.0",
				environment: process.env.NODE_ENV || "development",
				services,
				metrics: {
					databaseResponseTime: -1,
				},
				error: "Database connection failed",
			});
		}
	} catch (error) {
		console.error("Health check failed:", error);
		return apiInternalError("Service health check failed");
	}
}
