import { relations } from "drizzle-orm";
import {
	boolean,
	integer,
	numeric,
	pgTable,
	text,
	timestamp,
	varchar,
} from "drizzle-orm/pg-core";

const commonColumns = {
	id: varchar("id", { length: 255 })
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID()),
	createdAt: timestamp("created_at").notNull().defaultNow(),
	updatedAt: timestamp("updated_at")
		.notNull()
		.defaultNow()
		.$onUpdate(() => new Date()),
	deletedAt: timestamp("deleted_at"),
};

/**
 * User table for authentication (Better Auth) - Unified for internal and client users
 * @see https://github.com/better-auth/better-auth/blob/main/packages/cli/test/__snapshots__/auth-schema.txt
 */
export const users = pgTable("users", {
	...commonColumns,
	name: varchar("name", { length: 255 }).notNull(),
	email: varchar("email", { length: 255 }).notNull().unique(),
	emailVerified: boolean("email_verified").default(false).notNull(),
	image: varchar("image", { length: 255 }),
	twoFactorEnabled: boolean("two_factor_enabled"),
	username: varchar("username", { length: 255 }).unique(),
	displayUsername: varchar("display_username", { length: 255 }),
	// Unified authentication fields
	userType: varchar("user_type", { length: 20 }).notNull().default("internal"), // 'internal' or 'client'
	clientId: varchar("client_id").references(() => clients.id, {
		onDelete: "cascade",
	}), // Only for client users
	organizationId: varchar("organization_id").references(
		() => organizations.id,
		{ onDelete: "cascade" },
	), // For data isolation
	isActive: boolean("is_active").default(true),
	lastLogin: timestamp("last_login"),
});

/**
 * Session table for authentication (Better Auth)
 */
export const sessions = pgTable("sessions", {
	...commonColumns,
	expiresAt: timestamp("expires_at").notNull(),
	token: varchar("token", { length: 255 }).notNull().unique(),
	ipAddress: varchar("ip_address", { length: 255 }),
	userAgent: varchar("user_agent", { length: 255 }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Account table for OAuth and password providers (Better Auth)
 */
export const accounts = pgTable("accounts", {
	...commonColumns,
	accountId: varchar("account_id", { length: 255 }).notNull(),
	providerId: varchar("provider_id", { length: 255 }).notNull(),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	accessToken: varchar("access_token", { length: 255 }),
	refreshToken: varchar("refresh_token", { length: 255 }),
	idToken: varchar("id_token", { length: 255 }),
	accessTokenExpiresAt: timestamp("access_token_expires_at"),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
	scope: varchar("scope", { length: 255 }),
	password: varchar("password", { length: 255 }),
});

/**
 * Verification table for email/phone verification (Better Auth)
 */
export const verifications = pgTable("verifications", {
	...commonColumns,
	identifier: varchar("identifier", { length: 255 }).notNull(),
	value: varchar("value", { length: 255 }).notNull(),
	expiresAt: timestamp("expires_at").notNull(),
});

/**
 * Two-factor authentication table (Better Auth)
 */
export const twoFactors = pgTable("two_factors", {
	...commonColumns,
	secret: varchar("secret", { length: 255 }).notNull(),
	backupCodes: varchar("backup_codes", { length: 255 }).notNull(),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

const authSchema = {
	users,
	sessions,
	accounts,
	verifications,
	twoFactors,
};

export { authSchema };

export const teams = pgTable("teams", {
	...commonColumns,
	name: varchar("name", { length: 255 }).notNull(),
	ownerId: varchar("owner_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	description: text("description"),
	avatar: varchar("avatar", { length: 255 }),
});

export const teamMembers = pgTable("team_members", {
	...commonColumns,
	teamId: varchar("team_id")
		.notNull()
		.references(() => teams.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	role: varchar("role", { length: 255 }).notNull(),
});

export const teamInvitations = pgTable("team_invitations", {
	...commonColumns,
	teamId: varchar("team_id")
		.notNull()
		.references(() => teams.id, { onDelete: "cascade" }),
	email: varchar("email", { length: 255 }).notNull(),
	invitedById: varchar("invited_by_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	expiresAt: timestamp("expires_at").notNull(),
	acceptedAt: timestamp("accepted_at"),
	rejectedAt: timestamp("rejected_at"),
	invoiteToken: varchar("invite_token", { length: 255 }).notNull().unique(),
});

export const teamMemberPermissions = pgTable("team_member_permissions", {
	...commonColumns,
	teamMemberId: varchar("team_member_id")
		.notNull()
		.references(() => teamMembers.id, { onDelete: "cascade" }),
	permission: varchar("permission", { length: 255 }).notNull(),
});

export const organizations = pgTable("organizations", {
	...commonColumns,
	name: varchar("name", { length: 255 }).notNull(),
	ownerId: varchar("owner_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	description: text("description"),
	avatar: varchar("avatar", { length: 255 }),
});

export const organizationMembers = pgTable("organization_members", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	role: varchar("role", { length: 255 }).notNull(),
});

export const organizationInvitations = pgTable("organization_invitations", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	email: varchar("email", { length: 255 }).notNull(),
	invitedById: varchar("invited_by_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	expiresAt: timestamp("expires_at").notNull(),
	acceptedAt: timestamp("accepted_at"),
	rejectedAt: timestamp("rejected_at"),
	invoiteToken: varchar("invite_token", { length: 255 }).notNull().unique(),
});

export const organizationMemberPermissions = pgTable(
	"organization_member_permissions",
	{
		...commonColumns,
		organizationMemberId: varchar("organization_member_id")
			.notNull()
			.references(() => organizationMembers.id, { onDelete: "cascade" }),
		permission: varchar("permission", { length: 255 }).notNull(),
	},
);

export const clients = pgTable("clients", {
	...commonColumns,
	name: varchar("name", { length: 255 }).notNull(),
	companyName: varchar("company_name", { length: 255 }),
	email: varchar("email", { length: 255 }),
	phone: varchar("phone", { length: 50 }),
	website: varchar("website", { length: 255 }),
	description: text("description"),
	avatar: varchar("avatar", { length: 255 }),
	status: varchar("status", { length: 20 }).default("active"), // active, inactive, archived
	ownerId: varchar("owner_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
});

export const clientBillingAddress = pgTable("client_billing_address", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	line1: varchar("line1", { length: 255 }).notNull(),
	line2: varchar("line2", { length: 255 }),
	city: varchar("city", { length: 255 }).notNull(),
	state: varchar("state", { length: 255 }).notNull(),
	zip: varchar("zip", { length: 255 }).notNull(),
	country: varchar("country", { length: 255 }).notNull(),
});

export const projects = pgTable("projects", {
	...commonColumns,
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	status: varchar("status", { length: 50 }).notNull().default("active"), // active, completed, on_hold, cancelled
	startDate: timestamp("start_date"),
	endDate: timestamp("end_date"),
	budget: numeric("budget", { precision: 10, scale: 2 }),
	currency: varchar("currency", { length: 3 }).default("USD"),
	ownerId: varchar("owner_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
});

export const projectTasks = pgTable("project_tasks", {
	...commonColumns,
	projectId: varchar("project_id")
		.notNull()
		.references(() => projects.id, { onDelete: "cascade" }),
	sprintId: varchar("sprint_id").references(() => projectSprints.id, {
		onDelete: "set null",
	}),
	parentTaskId: varchar("parent_task_id").references(() => projectTasks.id, {
		onDelete: "cascade",
	}),
	statusId: varchar("status_id").references(() => taskStatuses.id, {
		onDelete: "restrict",
	}),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	priority: varchar("priority", { length: 20 }).default("medium"), // low, medium, high, urgent
	estimatedHours: numeric("estimated_hours", { precision: 5, scale: 2 }),
	actualHours: numeric("actual_hours", { precision: 5, scale: 2 }),
	dueDate: timestamp("due_date"),
	startedAt: timestamp("started_at"),
	completedAt: timestamp("completed_at"),
	order: integer("order").default(0),
});

export const projectTaskAssignees = pgTable("project_task_assignees", {
	...commonColumns,
	taskId: varchar("task_id")
		.notNull()
		.references(() => projectTasks.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

export const projectTaskComments = pgTable("project_task_comments", {
	...commonColumns,
	taskId: varchar("task_id")
		.notNull()
		.references(() => projectTasks.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	parentId: varchar("parent_id").references(() => projectTaskComments.id, {
		onDelete: "cascade",
	}),
	comment: text("comment").notNull(),
});

export const projectTaskAttachments = pgTable("project_task_attachments", {
	...commonColumns,
	taskId: varchar("task_id")
		.notNull()
		.references(() => projectTasks.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	url: varchar("url", { length: 255 }).notNull(),
});

export const invoices = pgTable("invoices", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	projectId: varchar("project_id").references(() => projects.id, {
		onDelete: "set null",
	}),
	number: varchar("number", { length: 255 }).notNull().unique(),
	title: varchar("title", { length: 255 }),
	date: timestamp("date").notNull(),
	dueDate: timestamp("due_date").notNull(),
	subtotal: numeric("subtotal", { precision: 10, scale: 2 }).notNull(),
	taxAmount: numeric("tax_amount", { precision: 10, scale: 2 }).default("0"),
	discountAmount: numeric("discount_amount", {
		precision: 10,
		scale: 2,
	}).default("0"),
	totalAmount: numeric("total_amount", { precision: 10, scale: 2 }).notNull(),
	currency: varchar("currency", { length: 3 }).default("USD"),
	status: varchar("status", { length: 20 }).notNull().default("draft"), // draft, sent, paid, overdue, cancelled
	notes: text("notes"),
	terms: text("terms"),
	paidAt: timestamp("paid_at"),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
});

export const invoiceItems = pgTable("invoice_items", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	description: text("description").notNull(),
	quantity: numeric("quantity").notNull(),
	unitPrice: numeric("unit_price").notNull(),
	amount: numeric("amount").notNull(),
});

// ============================================================================
// CORE PROJECT MANAGEMENT ENHANCEMENTS
// ============================================================================

/**
 * Project sprints for agile project management
 */
export const projectSprints = pgTable("project_sprints", {
	...commonColumns,
	projectId: varchar("project_id")
		.notNull()
		.references(() => projects.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	startDate: timestamp("start_date").notNull(),
	endDate: timestamp("end_date").notNull(),
	status: varchar("status", { length: 20 }).default("planned"), // planned, active, completed, cancelled
	goal: text("goal"),
	order: integer("order").default(0),
});

/**
 * Custom task statuses per organization
 */
export const taskStatuses = pgTable("task_statuses", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 100 }).notNull(),
	color: varchar("color", { length: 7 }).default("#6B7280"), // hex color
	order: integer("order").default(0),
	isDefault: boolean("is_default").default(false),
	isCompleted: boolean("is_completed").default(false), // marks task as completed
});

/**
 * Task dependencies for complex project management
 */
export const taskDependencies = pgTable("task_dependencies", {
	...commonColumns,
	taskId: varchar("task_id")
		.notNull()
		.references(() => projectTasks.id, { onDelete: "cascade" }),
	dependsOnTaskId: varchar("depends_on_task_id")
		.notNull()
		.references(() => projectTasks.id, { onDelete: "cascade" }),
	dependencyType: varchar("dependency_type", { length: 20 }).default(
		"finish_to_start",
	), // finish_to_start, start_to_start, etc.
});

/**
 * Project-level comments
 */
export const projectComments = pgTable("project_comments", {
	...commonColumns,
	projectId: varchar("project_id")
		.notNull()
		.references(() => projects.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	parentId: varchar("parent_id").references(() => projectComments.id, {
		onDelete: "cascade",
	}),
	comment: text("comment").notNull(),
});

// ============================================================================
// CLIENT MANAGEMENT ENHANCEMENTS
// ============================================================================

/**
 * Multiple contact persons per client
 */
export const clientContacts = pgTable("client_contacts", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	email: varchar("email", { length: 255 }),
	phone: varchar("phone", { length: 50 }),
	position: varchar("position", { length: 255 }),
	isPrimary: boolean("is_primary").default(false),
});

/**
 * Client communication preferences and settings
 */
export const clientPreferences = pgTable("client_preferences", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	communicationMethod: varchar("communication_method", { length: 50 }).default(
		"email",
	), // email, phone, slack
	timezone: varchar("timezone", { length: 100 }).default("UTC"),
	language: varchar("language", { length: 10 }).default("en"),
	notificationSettings: text("notification_settings"), // JSON string
});

/**
 * Client notifications for the unified auth system
 */
export const clientNotifications = pgTable("client_notifications", {
	...commonColumns,
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }), // Using unified auth
	type: varchar("type", { length: 50 }).notNull(), // project_update, invoice_sent, etc.
	title: varchar("title", { length: 255 }).notNull(),
	message: text("message").notNull(),
	isRead: boolean("is_read").default(false),
	readAt: timestamp("read_at"),
	actionUrl: varchar("action_url", { length: 500 }),
});

// ============================================================================
// FINANCIAL MANAGEMENT EXTENSIONS
// ============================================================================

/**
 * Expense categories for business expense tracking
 */
export const expenseCategories = pgTable("expense_categories", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	isActive: boolean("is_active").default(true),
});

/**
 * Business receipts and expense tracking
 */
export const receipts = pgTable("receipts", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	vendorName: varchar("vendor_name", { length: 255 }).notNull(),
	amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
	currency: varchar("currency", { length: 3 }).default("USD"),
	receiptDate: timestamp("receipt_date").notNull(),
	categoryId: varchar("category_id").references(() => expenseCategories.id, {
		onDelete: "set null",
	}),
	description: text("description"),
	fileUrl: varchar("file_url", { length: 500 }),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Invoice payments tracking
 */
export const invoicePayments = pgTable("invoice_payments", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
	paymentDate: timestamp("payment_date").notNull(),
	paymentMethod: varchar("payment_method", { length: 50 }), // bank_transfer, credit_card, paypal, etc.
	transactionId: varchar("transaction_id", { length: 255 }),
	notes: text("notes"),
});

/**
 * Automated payment reminders
 */
export const paymentReminders = pgTable("payment_reminders", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	reminderType: varchar("reminder_type", { length: 50 }).notNull(), // first, second, final, custom
	sentAt: timestamp("sent_at").notNull(),
	status: varchar("status", { length: 20 }).default("sent"), // sent, delivered, opened, failed
});

/**
 * Late fees for overdue invoices
 */
export const lateFees = pgTable("late_fees", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	feeAmount: numeric("fee_amount", { precision: 10, scale: 2 }).notNull(),
	appliedDate: timestamp("applied_date").notNull(),
	reason: text("reason"),
});

// ============================================================================
// CONTRACT MANAGEMENT SYSTEM
// ============================================================================

/**
 * Contract templates for reusable contract generation
 */
export const contractTemplates = pgTable("contract_templates", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	content: text("content").notNull(), // HTML/Markdown template
	variables: text("variables"), // JSON array of template variables
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Client contracts with digital signature support
 */
export const contracts = pgTable("contracts", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	projectId: varchar("project_id").references(() => projects.id, {
		onDelete: "set null",
	}),
	templateId: varchar("template_id").references(() => contractTemplates.id, {
		onDelete: "set null",
	}),
	title: varchar("title", { length: 255 }).notNull(),
	content: text("content").notNull(),
	status: varchar("status", { length: 20 }).default("draft"), // draft, sent, signed, expired, cancelled
	signedDate: timestamp("signed_date"),
	expiresDate: timestamp("expires_date"),
	value: numeric("value", { precision: 10, scale: 2 }),
	currency: varchar("currency", { length: 3 }).default("USD"),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Digital signatures for contracts
 */
export const contractSignatures = pgTable("contract_signatures", {
	...commonColumns,
	contractId: varchar("contract_id")
		.notNull()
		.references(() => contracts.id, { onDelete: "cascade" }),
	signerName: varchar("signer_name", { length: 255 }).notNull(),
	signerEmail: varchar("signer_email", { length: 255 }).notNull(),
	signatureData: text("signature_data"), // Base64 signature image or digital signature
	signedAt: timestamp("signed_at").notNull(),
	ipAddress: varchar("ip_address", { length: 255 }),
});

// ============================================================================
// SUPPORT TICKET SYSTEM (Using Unified Authentication)
// ============================================================================

/**
 * Support tickets for client communication
 */
export const supportTickets = pgTable("support_tickets", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	projectId: varchar("project_id").references(() => projects.id, {
		onDelete: "set null",
	}),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description").notNull(),
	priority: varchar("priority", { length: 20 }).default("medium"), // low, medium, high, urgent
	status: varchar("status", { length: 20 }).default("open"), // open, in_progress, resolved, closed
	assignedTo: varchar("assigned_to").references(() => users.id, {
		onDelete: "set null",
	}),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }), // Using unified auth - can be client or internal user
	resolvedAt: timestamp("resolved_at"),
	closedAt: timestamp("closed_at"),
});

/**
 * Ticket messages/replies
 */
export const ticketMessages = pgTable("ticket_messages", {
	...commonColumns,
	ticketId: varchar("ticket_id")
		.notNull()
		.references(() => supportTickets.id, { onDelete: "cascade" }),
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }), // Using unified auth
	message: text("message").notNull(),
	isInternal: boolean("is_internal").default(false), // internal notes vs client-visible
});

/**
 * Ticket attachments
 */
export const ticketAttachments = pgTable("ticket_attachments", {
	...commonColumns,
	ticketId: varchar("ticket_id")
		.notNull()
		.references(() => supportTickets.id, { onDelete: "cascade" }),
	messageId: varchar("message_id").references(() => ticketMessages.id, {
		onDelete: "cascade",
	}),
	filename: varchar("filename", { length: 255 }).notNull(),
	fileUrl: varchar("file_url", { length: 500 }).notNull(),
	fileSize: integer("file_size"),
	mimeType: varchar("mime_type", { length: 100 }),
});

// ============================================================================
// THIRD-PARTY INTEGRATIONS & DOCUMENT MANAGEMENT
// ============================================================================

/**
 * Third-party service integrations (Slack, Google Drive, GitHub, etc.)
 */
export const integrations = pgTable("integrations", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	provider: varchar("provider", { length: 50 }).notNull(), // slack, google_drive, github, etc.
	name: varchar("name", { length: 255 }).notNull(),
	configData: text("config_data"), // JSON configuration
	isActive: boolean("is_active").default(true),
	lastSyncAt: timestamp("last_sync_at"),
});

/**
 * Client access to third-party integrations
 */
export const clientIntegrationAccess = pgTable("client_integration_access", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	integrationId: varchar("integration_id")
		.notNull()
		.references(() => integrations.id, { onDelete: "cascade" }),
	accessLevel: varchar("access_level", { length: 20 }).default("read"), // read, write, admin
	grantedBy: varchar("granted_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	grantedAt: timestamp("granted_at").notNull(),
	expiresAt: timestamp("expires_at"),
});

/**
 * Document templates for PDF generation
 */
export const documentTemplates = pgTable("document_templates", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	type: varchar("type", { length: 50 }).notNull(), // invoice, proposal, contract, eta
	name: varchar("name", { length: 255 }).notNull(),
	templateData: text("template_data").notNull(), // HTML/JSON template
	variables: text("variables"), // JSON array of available variables
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Generated documents (PDFs, etc.)
 */
export const generatedDocuments = pgTable("generated_documents", {
	...commonColumns,
	templateId: varchar("template_id").references(() => documentTemplates.id, {
		onDelete: "set null",
	}),
	entityType: varchar("entity_type", { length: 50 }).notNull(), // project, invoice, contract
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	filename: varchar("filename", { length: 255 }).notNull(),
	fileUrl: varchar("file_url", { length: 500 }).notNull(),
	fileSize: integer("file_size"),
	generatedBy: varchar("generated_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	generatedAt: timestamp("generated_at").notNull(),
});

/**
 * File uploads management system
 */
export const fileUploads = pgTable("file_uploads", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	uploadedBy: varchar("uploaded_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	filename: varchar("filename", { length: 255 }).notNull(),
	originalName: varchar("original_name", { length: 255 }).notNull(),
	filePath: varchar("file_path", { length: 500 }).notNull(),
	fileSize: integer("file_size").notNull(),
	mimeType: varchar("mime_type", { length: 100 }).notNull(),
	entityType: varchar("entity_type", { length: 50 }), // project, task, ticket, etc.
	entityId: varchar("entity_id", { length: 255 }),
});

// ============================================================================
// MULTI-ORGANIZATION SUPPORT EXTENSIONS
// ============================================================================

/**
 * User memberships across multiple organizations
 * Replaces the single organizationId in users table for cross-org support
 */
export const userOrganizations = pgTable("user_organizations", {
	...commonColumns,
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	role: varchar("role", { length: 50 }).notNull().default("member"), // owner, admin, member, viewer
	isPrimary: boolean("is_primary").default(false), // Primary organization for user
	joinedAt: timestamp("joined_at").notNull().defaultNow(),
	isActive: boolean("is_active").default(true),
});

/**
 * Client relationships across multiple organizations
 * Allows clients to work with multiple service providers
 */
export const clientOrganizations = pgTable("client_organizations", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	relationshipType: varchar("relationship_type", { length: 50 }).default(
		"client",
	), // client, partner, vendor
	status: varchar("status", { length: 20 }).default("active"), // active, inactive, suspended
	startDate: timestamp("start_date").notNull().defaultNow(),
	endDate: timestamp("end_date"),
	notes: text("notes"),
});

// ============================================================================
// PROJECT TEMPLATE SYSTEM
// ============================================================================

/**
 * Reusable project templates for consistent project creation
 */
export const projectTemplates = pgTable("project_templates", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	defaultBudget: numeric("default_budget", { precision: 10, scale: 2 }),
	defaultCurrency: varchar("default_currency", { length: 3 }).default("USD"),
	estimatedDuration: integer("estimated_duration"), // in days
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Template task definitions for project templates
 */
export const projectTemplateTask = pgTable("project_template_tasks", {
	...commonColumns,
	templateId: varchar("template_id")
		.notNull()
		.references(() => projectTemplates.id, { onDelete: "cascade" }),
	parentTaskId: varchar("parent_task_id").references(
		() => projectTemplateTask.id,
		{
			onDelete: "cascade",
		},
	),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	priority: varchar("priority", { length: 20 }).default("medium"),
	estimatedHours: numeric("estimated_hours", { precision: 5, scale: 2 }),
	order: integer("order").default(0),
	dayOffset: integer("day_offset").default(0), // Days from project start
});

// ============================================================================
// CALENDAR INTEGRATION SYSTEM
// ============================================================================

/**
 * Calendar events for tasks, deadlines, and meetings
 */
export const calendarEvents = pgTable("calendar_events", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	startTime: timestamp("start_time").notNull(),
	endTime: timestamp("end_time").notNull(),
	isAllDay: boolean("is_all_day").default(false),
	timezone: varchar("timezone", { length: 100 }).default("UTC"),
	// Entity associations
	entityType: varchar("entity_type", { length: 50 }), // task, project, invoice, meeting
	entityId: varchar("entity_id", { length: 255 }),
	// External calendar sync
	externalCalendarId: varchar("external_calendar_id", { length: 255 }),
	externalEventId: varchar("external_event_id", { length: 255 }),
	syncProvider: varchar("sync_provider", { length: 50 }), // google, outlook, apple
	lastSyncAt: timestamp("last_sync_at"),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Calendar event attendees
 */
export const calendarEventAttendees = pgTable("calendar_event_attendees", {
	...commonColumns,
	eventId: varchar("event_id")
		.notNull()
		.references(() => calendarEvents.id, { onDelete: "cascade" }),
	userId: varchar("user_id").references(() => users.id, {
		onDelete: "cascade",
	}),
	email: varchar("email", { length: 255 }), // For external attendees
	name: varchar("name", { length: 255 }),
	status: varchar("status", { length: 20 }).default("pending"), // pending, accepted, declined, tentative
	isOrganizer: boolean("is_organizer").default(false),
});

// ============================================================================
// FREELANCER MODE SUPPORT
// ============================================================================

/**
 * Freelancer profiles for single-user operations
 */
export const freelancerProfiles = pgTable("freelancer_profiles", {
	...commonColumns,
	userId: varchar("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	businessName: varchar("business_name", { length: 255 }),
	tagline: varchar("tagline", { length: 500 }),
	bio: text("bio"),
	website: varchar("website", { length: 255 }),
	hourlyRate: numeric("hourly_rate", { precision: 8, scale: 2 }),
	currency: varchar("currency", { length: 3 }).default("USD"),
	skills: text("skills"), // JSON array of skills
	availability: varchar("availability", { length: 50 }).default("available"), // available, busy, unavailable
	timezone: varchar("timezone", { length: 100 }).default("UTC"),
	// Tax information
	taxId: varchar("tax_id", { length: 50 }),
	taxIdType: varchar("tax_id_type", { length: 20 }), // ssn, ein, vat, etc.
	// Business address
	businessAddress: text("business_address"),
	isActive: boolean("is_active").default(true),
});

// ============================================================================
// TAX MANAGEMENT SYSTEM
// ============================================================================

/**
 * Tax rates and rules per organization/location
 */
export const taxRates = pgTable("tax_rates", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	name: varchar("name", { length: 255 }).notNull(), // "Sales Tax", "VAT", "GST"
	rate: numeric("rate", { precision: 5, scale: 4 }).notNull(), // 0.0825 for 8.25%
	type: varchar("type", { length: 20 }).notNull(), // percentage, fixed
	country: varchar("country", { length: 2 }), // ISO country code
	state: varchar("state", { length: 50 }),
	city: varchar("city", { length: 100 }),
	zipCode: varchar("zip_code", { length: 20 }),
	isDefault: boolean("is_default").default(false),
	isActive: boolean("is_active").default(true),
	effectiveDate: timestamp("effective_date").notNull(),
	expiryDate: timestamp("expiry_date"),
});

/**
 * Tax line items for invoices
 */
export const invoiceTaxes = pgTable("invoice_taxes", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	taxRateId: varchar("tax_rate_id")
		.notNull()
		.references(() => taxRates.id, { onDelete: "restrict" }),
	taxableAmount: numeric("taxable_amount", {
		precision: 10,
		scale: 2,
	}).notNull(),
	taxAmount: numeric("tax_amount", { precision: 10, scale: 2 }).notNull(),
	description: varchar("description", { length: 255 }),
});

// ============================================================================
// PROPOSAL SYSTEM WITH APPROVAL WORKFLOWS
// ============================================================================

/**
 * Project proposals with approval workflows
 */
export const proposals = pgTable("proposals", {
	...commonColumns,
	clientId: varchar("client_id")
		.notNull()
		.references(() => clients.id, { onDelete: "cascade" }),
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description").notNull(),
	scope: text("scope"), // Detailed scope of work
	deliverables: text("deliverables"), // JSON array of deliverables
	timeline: text("timeline"), // JSON timeline structure
	totalAmount: numeric("total_amount", { precision: 10, scale: 2 }).notNull(),
	currency: varchar("currency", { length: 3 }).default("USD"),
	status: varchar("status", { length: 20 }).default("draft"), // draft, sent, viewed, accepted, rejected, expired
	validUntil: timestamp("valid_until"),
	sentAt: timestamp("sent_at"),
	viewedAt: timestamp("viewed_at"),
	respondedAt: timestamp("responded_at"),
	notes: text("notes"),
	createdBy: varchar("created_by")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Proposal line items/sections
 */
export const proposalItems = pgTable("proposal_items", {
	...commonColumns,
	proposalId: varchar("proposal_id")
		.notNull()
		.references(() => proposals.id, { onDelete: "cascade" }),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	quantity: numeric("quantity", { precision: 8, scale: 2 }).default("1"),
	unitPrice: numeric("unit_price", { precision: 10, scale: 2 }).notNull(),
	amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
	order: integer("order").default(0),
});

/**
 * Proposal approval workflow
 */
export const proposalApprovals = pgTable("proposal_approvals", {
	...commonColumns,
	proposalId: varchar("proposal_id")
		.notNull()
		.references(() => proposals.id, { onDelete: "cascade" }),
	approverUserId: varchar("approver_user_id").references(() => users.id, {
		onDelete: "set null",
	}),
	approverEmail: varchar("approver_email", { length: 255 }),
	approverName: varchar("approver_name", { length: 255 }),
	status: varchar("status", { length: 20 }).default("pending"), // pending, approved, rejected
	comments: text("comments"),
	approvedAt: timestamp("approved_at"),
	order: integer("order").default(0), // For sequential approvals
});

// ============================================================================
// PAYMENT GATEWAY INTEGRATION
// ============================================================================

/**
 * Payment gateway configurations per organization
 */
export const paymentGateways = pgTable("payment_gateways", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	provider: varchar("provider", { length: 50 }).notNull(), // stripe, paypal, square, etc.
	name: varchar("name", { length: 255 }).notNull(),
	isActive: boolean("is_active").default(true),
	isDefault: boolean("is_default").default(false),
	configData: text("config_data"), // Encrypted JSON configuration
	supportedCurrencies: text("supported_currencies"), // JSON array
	supportedMethods: text("supported_methods"), // JSON array: card, bank, wallet
	webhookUrl: varchar("webhook_url", { length: 500 }),
	webhookSecret: varchar("webhook_secret", { length: 255 }),
	lastSyncAt: timestamp("last_sync_at"),
});

/**
 * Payment transactions for automated invoice processing
 */
export const paymentTransactions = pgTable("payment_transactions", {
	...commonColumns,
	invoiceId: varchar("invoice_id")
		.notNull()
		.references(() => invoices.id, { onDelete: "cascade" }),
	gatewayId: varchar("gateway_id")
		.notNull()
		.references(() => paymentGateways.id, { onDelete: "restrict" }),
	externalTransactionId: varchar("external_transaction_id", {
		length: 255,
	}).notNull(),
	amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
	currency: varchar("currency", { length: 3 }).notNull(),
	status: varchar("status", { length: 20 }).notNull(), // pending, completed, failed, refunded
	paymentMethod: varchar("payment_method", { length: 50 }), // card, bank_transfer, paypal, etc.
	gatewayFee: numeric("gateway_fee", { precision: 10, scale: 2 }),
	netAmount: numeric("net_amount", { precision: 10, scale: 2 }),
	processedAt: timestamp("processed_at"),
	failureReason: text("failure_reason"),
	metadata: text("metadata"), // JSON for additional gateway data
});

// ============================================================================
// AUDIT TRAILS & ACTIVITY TRACKING
// ============================================================================

/**
 * Comprehensive activity logging for audit trails
 */
export const activityLogs = pgTable("activity_logs", {
	...commonColumns,
	organizationId: varchar("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	userId: varchar("user_id").references(() => users.id, {
		onDelete: "set null",
	}),
	entityType: varchar("entity_type", { length: 50 }).notNull(),
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	action: varchar("action", { length: 50 }).notNull(), // create, update, delete, etc.
	oldValues: text("old_values"), // JSON of previous values
	newValues: text("new_values"), // JSON of new values
	ipAddress: varchar("ip_address", { length: 255 }),
	userAgent: varchar("user_agent", { length: 500 }),
});

// ============================================================================
// DRIZZLE RELATIONS FOR OPTIMIZED QUERIES
// ============================================================================

/**
 * User relations - central to the unified auth system
 */
export const usersRelations = relations(users, ({ one, many }) => ({
	// Organization membership (legacy single org - to be deprecated)
	organization: one(organizations, {
		fields: [users.organizationId],
		references: [organizations.id],
	}),
	// Client relationship (for client users)
	client: one(clients, {
		fields: [users.clientId],
		references: [clients.id],
	}),
	// User activities
	sessions: many(sessions),
	accounts: many(accounts),
	twoFactors: many(twoFactors),
	// Business relationships
	ownedOrganizations: many(organizations),
	ownedTeams: many(teams),
	ownedClients: many(clients),
	ownedProjects: many(projects),
	// Activity tracking
	activityLogs: many(activityLogs),
	notifications: many(clientNotifications),
	// Multi-organization support
	organizationMemberships: many(userOrganizations),
	// New features
	freelancerProfile: one(freelancerProfiles),
	createdProposals: many(proposals),
	createdCalendarEvents: many(calendarEvents),
	calendarAttendances: many(calendarEventAttendees),
}));

/**
 * Organization relations - multi-tenant root
 */
export const organizationsRelations = relations(
	organizations,
	({ one, many }) => ({
		owner: one(users, {
			fields: [organizations.ownerId],
			references: [users.id],
		}),
		members: many(organizationMembers),
		invitations: many(organizationInvitations),
		clients: many(clients),
		projects: many(projects),
		taskStatuses: many(taskStatuses),
		integrations: many(integrations),
		contractTemplates: many(contractTemplates),
		documentTemplates: many(documentTemplates),
		expenseCategories: many(expenseCategories),
		receipts: many(receipts),
		fileUploads: many(fileUploads),
		activityLogs: many(activityLogs),
		// Multi-organization support
		userMemberships: many(userOrganizations),
		clientRelationships: many(clientOrganizations),
		// New features
		projectTemplates: many(projectTemplates),
		calendarEvents: many(calendarEvents),
		taxRates: many(taxRates),
		proposals: many(proposals),
		paymentGateways: many(paymentGateways),
	}),
);

/**
 * Client relations
 */
export const clientsRelations = relations(clients, ({ one, many }) => ({
	organization: one(organizations, {
		fields: [clients.organizationId],
		references: [organizations.id],
	}),
	owner: one(users, {
		fields: [clients.ownerId],
		references: [users.id],
	}),
	// Client data
	billingAddress: one(clientBillingAddress),
	contacts: many(clientContacts),
	preferences: one(clientPreferences),
	// Business relationships
	projects: many(projects),
	contracts: many(contracts),
	invoices: many(invoices),
	supportTickets: many(supportTickets),
	integrationAccess: many(clientIntegrationAccess),
	// Client users (unified auth)
	users: many(users),
	// Multi-organization support
	organizationRelationships: many(clientOrganizations),
	// New features
	proposals: many(proposals),
}));

/**
 * Project relations
 */
export const projectsRelations = relations(projects, ({ one, many }) => ({
	organization: one(organizations, {
		fields: [projects.organizationId],
		references: [organizations.id],
	}),
	client: one(clients, {
		fields: [projects.clientId],
		references: [clients.id],
	}),
	owner: one(users, {
		fields: [projects.ownerId],
		references: [users.id],
	}),
	// Project components
	sprints: many(projectSprints),
	tasks: many(projectTasks),
	comments: many(projectComments),
	contracts: many(contracts),
	invoices: many(invoices),
	supportTickets: many(supportTickets),
}));

/**
 * Project task relations
 */
export const projectTasksRelations = relations(
	projectTasks,
	({ one, many }) => ({
		project: one(projects, {
			fields: [projectTasks.projectId],
			references: [projects.id],
		}),
		sprint: one(projectSprints, {
			fields: [projectTasks.sprintId],
			references: [projectSprints.id],
		}),
		status: one(taskStatuses, {
			fields: [projectTasks.statusId],
			references: [taskStatuses.id],
		}),
		parentTask: one(projectTasks, {
			fields: [projectTasks.parentTaskId],
			references: [projectTasks.id],
		}),
		// Task components
		subtasks: many(projectTasks),
		assignees: many(projectTaskAssignees),
		comments: many(projectTaskComments),
		attachments: many(projectTaskAttachments),
		dependencies: many(taskDependencies),
		dependentTasks: many(taskDependencies),
	}),
);

/**
 * Invoice relations
 */
export const invoicesRelations = relations(invoices, ({ one, many }) => ({
	organization: one(organizations, {
		fields: [invoices.organizationId],
		references: [organizations.id],
	}),
	client: one(clients, {
		fields: [invoices.clientId],
		references: [clients.id],
	}),
	project: one(projects, {
		fields: [invoices.projectId],
		references: [projects.id],
	}),
	// Invoice components
	items: many(invoiceItems),
	payments: many(invoicePayments),
	reminders: many(paymentReminders),
	lateFees: many(lateFees),
	taxes: many(invoiceTaxes),
	transactions: many(paymentTransactions),
}));

// ============================================================================
// NEW TABLE RELATIONS
// ============================================================================

/**
 * User Organizations relations (many-to-many)
 */
export const userOrganizationsRelations = relations(
	userOrganizations,
	({ one }) => ({
		user: one(users, {
			fields: [userOrganizations.userId],
			references: [users.id],
		}),
		organization: one(organizations, {
			fields: [userOrganizations.organizationId],
			references: [organizations.id],
		}),
	}),
);

/**
 * Client Organizations relations (many-to-many)
 */
export const clientOrganizationsRelations = relations(
	clientOrganizations,
	({ one }) => ({
		client: one(clients, {
			fields: [clientOrganizations.clientId],
			references: [clients.id],
		}),
		organization: one(organizations, {
			fields: [clientOrganizations.organizationId],
			references: [organizations.id],
		}),
	}),
);

/**
 * Project Templates relations
 */
export const projectTemplatesRelations = relations(
	projectTemplates,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [projectTemplates.organizationId],
			references: [organizations.id],
		}),
		createdBy: one(users, {
			fields: [projectTemplates.createdBy],
			references: [users.id],
		}),
		tasks: many(projectTemplateTask),
	}),
);

/**
 * Project Template Tasks relations
 */
export const projectTemplateTaskRelations = relations(
	projectTemplateTask,
	({ one, many }) => ({
		template: one(projectTemplates, {
			fields: [projectTemplateTask.templateId],
			references: [projectTemplates.id],
		}),
		parentTask: one(projectTemplateTask, {
			fields: [projectTemplateTask.parentTaskId],
			references: [projectTemplateTask.id],
		}),
		subtasks: many(projectTemplateTask),
	}),
);

/**
 * Calendar Events relations
 */
export const calendarEventsRelations = relations(
	calendarEvents,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [calendarEvents.organizationId],
			references: [organizations.id],
		}),
		createdBy: one(users, {
			fields: [calendarEvents.createdBy],
			references: [users.id],
		}),
		attendees: many(calendarEventAttendees),
	}),
);

/**
 * Calendar Event Attendees relations
 */
export const calendarEventAttendeesRelations = relations(
	calendarEventAttendees,
	({ one }) => ({
		event: one(calendarEvents, {
			fields: [calendarEventAttendees.eventId],
			references: [calendarEvents.id],
		}),
		user: one(users, {
			fields: [calendarEventAttendees.userId],
			references: [users.id],
		}),
	}),
);

/**
 * Freelancer Profiles relations
 */
export const freelancerProfilesRelations = relations(
	freelancerProfiles,
	({ one }) => ({
		user: one(users, {
			fields: [freelancerProfiles.userId],
			references: [users.id],
		}),
	}),
);

/**
 * Tax Rates relations
 */
export const taxRatesRelations = relations(taxRates, ({ one, many }) => ({
	organization: one(organizations, {
		fields: [taxRates.organizationId],
		references: [organizations.id],
	}),
	invoiceTaxes: many(invoiceTaxes),
}));

/**
 * Invoice Taxes relations
 */
export const invoiceTaxesRelations = relations(invoiceTaxes, ({ one }) => ({
	invoice: one(invoices, {
		fields: [invoiceTaxes.invoiceId],
		references: [invoices.id],
	}),
	taxRate: one(taxRates, {
		fields: [invoiceTaxes.taxRateId],
		references: [taxRates.id],
	}),
}));

/**
 * Proposals relations
 */
export const proposalsRelations = relations(proposals, ({ one, many }) => ({
	client: one(clients, {
		fields: [proposals.clientId],
		references: [clients.id],
	}),
	organization: one(organizations, {
		fields: [proposals.organizationId],
		references: [organizations.id],
	}),
	createdBy: one(users, {
		fields: [proposals.createdBy],
		references: [users.id],
	}),
	items: many(proposalItems),
	approvals: many(proposalApprovals),
}));

/**
 * Proposal Items relations
 */
export const proposalItemsRelations = relations(proposalItems, ({ one }) => ({
	proposal: one(proposals, {
		fields: [proposalItems.proposalId],
		references: [proposals.id],
	}),
}));

/**
 * Proposal Approvals relations
 */
export const proposalApprovalsRelations = relations(
	proposalApprovals,
	({ one }) => ({
		proposal: one(proposals, {
			fields: [proposalApprovals.proposalId],
			references: [proposals.id],
		}),
		approver: one(users, {
			fields: [proposalApprovals.approverUserId],
			references: [users.id],
		}),
	}),
);

/**
 * Payment Gateways relations
 */
export const paymentGatewaysRelations = relations(
	paymentGateways,
	({ one, many }) => ({
		organization: one(organizations, {
			fields: [paymentGateways.organizationId],
			references: [organizations.id],
		}),
		transactions: many(paymentTransactions),
	}),
);

/**
 * Payment Transactions relations
 */
export const paymentTransactionsRelations = relations(
	paymentTransactions,
	({ one }) => ({
		invoice: one(invoices, {
			fields: [paymentTransactions.invoiceId],
			references: [invoices.id],
		}),
		gateway: one(paymentGateways, {
			fields: [paymentTransactions.gatewayId],
			references: [paymentGateways.id],
		}),
	}),
);
