{"id": "3e40e3b1-8297-4a03-84d5-04927e07ad01", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.activity_logs": {"name": "activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "old_values": {"name": "old_values", "type": "text", "primaryKey": false, "notNull": false}, "new_values": {"name": "new_values", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"activity_logs_organization_id_organizations_id_fk": {"name": "activity_logs_organization_id_organizations_id_fk", "tableFrom": "activity_logs", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "activity_logs_user_id_users_id_fk": {"name": "activity_logs_user_id_users_id_fk", "tableFrom": "activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.calendar_event_attendees": {"name": "calendar_event_attendees", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "is_organizer": {"name": "is_organizer", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"calendar_event_attendees_event_id_calendar_events_id_fk": {"name": "calendar_event_attendees_event_id_calendar_events_id_fk", "tableFrom": "calendar_event_attendees", "tableTo": "calendar_events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "calendar_event_attendees_user_id_users_id_fk": {"name": "calendar_event_attendees_user_id_users_id_fk", "tableFrom": "calendar_event_attendees", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.calendar_events": {"name": "calendar_events", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_all_day": {"name": "is_all_day", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_calendar_id": {"name": "external_calendar_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_event_id": {"name": "external_event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sync_provider": {"name": "sync_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"calendar_events_organization_id_organizations_id_fk": {"name": "calendar_events_organization_id_organizations_id_fk", "tableFrom": "calendar_events", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "calendar_events_created_by_users_id_fk": {"name": "calendar_events_created_by_users_id_fk", "tableFrom": "calendar_events", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_billing_address": {"name": "client_billing_address", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "line1": {"name": "line1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "line2": {"name": "line2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"client_billing_address_client_id_clients_id_fk": {"name": "client_billing_address_client_id_clients_id_fk", "tableFrom": "client_billing_address", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_contacts": {"name": "client_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"client_contacts_client_id_clients_id_fk": {"name": "client_contacts_client_id_clients_id_fk", "tableFrom": "client_contacts", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_integration_access": {"name": "client_integration_access", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "integration_id": {"name": "integration_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "access_level": {"name": "access_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'read'"}, "granted_by": {"name": "granted_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"client_integration_access_client_id_clients_id_fk": {"name": "client_integration_access_client_id_clients_id_fk", "tableFrom": "client_integration_access", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "client_integration_access_integration_id_integrations_id_fk": {"name": "client_integration_access_integration_id_integrations_id_fk", "tableFrom": "client_integration_access", "tableTo": "integrations", "columnsFrom": ["integration_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "client_integration_access_granted_by_users_id_fk": {"name": "client_integration_access_granted_by_users_id_fk", "tableFrom": "client_integration_access", "tableTo": "users", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_notifications": {"name": "client_notifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "action_url": {"name": "action_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"client_notifications_user_id_users_id_fk": {"name": "client_notifications_user_id_users_id_fk", "tableFrom": "client_notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_organizations": {"name": "client_organizations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'client'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"client_organizations_client_id_clients_id_fk": {"name": "client_organizations_client_id_clients_id_fk", "tableFrom": "client_organizations", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "client_organizations_organization_id_organizations_id_fk": {"name": "client_organizations_organization_id_organizations_id_fk", "tableFrom": "client_organizations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_preferences": {"name": "client_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "communication_method": {"name": "communication_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'email'"}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'en'"}, "notification_settings": {"name": "notification_settings", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"client_preferences_client_id_clients_id_fk": {"name": "client_preferences_client_id_clients_id_fk", "tableFrom": "client_preferences", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"clients_owner_id_users_id_fk": {"name": "clients_owner_id_users_id_fk", "tableFrom": "clients", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "clients_organization_id_organizations_id_fk": {"name": "clients_organization_id_organizations_id_fk", "tableFrom": "clients", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_signatures": {"name": "contract_signatures", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "contract_id": {"name": "contract_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "signer_name": {"name": "signer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "signer_email": {"name": "signer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "signature_data": {"name": "signature_data", "type": "text", "primaryKey": false, "notNull": false}, "signed_at": {"name": "signed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"contract_signatures_contract_id_contracts_id_fk": {"name": "contract_signatures_contract_id_contracts_id_fk", "tableFrom": "contract_signatures", "tableTo": "contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_templates": {"name": "contract_templates", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"contract_templates_organization_id_organizations_id_fk": {"name": "contract_templates_organization_id_organizations_id_fk", "tableFrom": "contract_templates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contract_templates_created_by_users_id_fk": {"name": "contract_templates_created_by_users_id_fk", "tableFrom": "contract_templates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contracts": {"name": "contracts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "template_id": {"name": "template_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'draft'"}, "signed_date": {"name": "signed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_date": {"name": "expires_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"contracts_client_id_clients_id_fk": {"name": "contracts_client_id_clients_id_fk", "tableFrom": "contracts", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contracts_project_id_projects_id_fk": {"name": "contracts_project_id_projects_id_fk", "tableFrom": "contracts", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "contracts_template_id_contract_templates_id_fk": {"name": "contracts_template_id_contract_templates_id_fk", "tableFrom": "contracts", "tableTo": "contract_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "contracts_created_by_users_id_fk": {"name": "contracts_created_by_users_id_fk", "tableFrom": "contracts", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_templates": {"name": "document_templates", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "template_data": {"name": "template_data", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"document_templates_organization_id_organizations_id_fk": {"name": "document_templates_organization_id_organizations_id_fk", "tableFrom": "document_templates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_templates_created_by_users_id_fk": {"name": "document_templates_created_by_users_id_fk", "tableFrom": "document_templates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.expense_categories": {"name": "expense_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"expense_categories_organization_id_organizations_id_fk": {"name": "expense_categories_organization_id_organizations_id_fk", "tableFrom": "expense_categories", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.file_uploads": {"name": "file_uploads", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "uploaded_by": {"name": "uploaded_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"file_uploads_organization_id_organizations_id_fk": {"name": "file_uploads_organization_id_organizations_id_fk", "tableFrom": "file_uploads", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "file_uploads_uploaded_by_users_id_fk": {"name": "file_uploads_uploaded_by_users_id_fk", "tableFrom": "file_uploads", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.freelancer_profiles": {"name": "freelancer_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "business_name": {"name": "business_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tagline": {"name": "tagline", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "hourly_rate": {"name": "hourly_rate", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'available'"}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "tax_id": {"name": "tax_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "tax_id_type": {"name": "tax_id_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "business_address": {"name": "business_address", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"freelancer_profiles_user_id_users_id_fk": {"name": "freelancer_profiles_user_id_users_id_fk", "tableFrom": "freelancer_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generated_documents": {"name": "generated_documents", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "template_id": {"name": "template_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "generated_by": {"name": "generated_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"generated_documents_template_id_document_templates_id_fk": {"name": "generated_documents_template_id_document_templates_id_fk", "tableFrom": "generated_documents", "tableTo": "document_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "generated_documents_generated_by_users_id_fk": {"name": "generated_documents_generated_by_users_id_fk", "tableFrom": "generated_documents", "tableTo": "users", "columnsFrom": ["generated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.integrations": {"name": "integrations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "config_data": {"name": "config_data", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"integrations_organization_id_organizations_id_fk": {"name": "integrations_organization_id_organizations_id_fk", "tableFrom": "integrations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoice_items": {"name": "invoice_items", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "numeric", "primaryKey": false, "notNull": true}, "unit_price": {"name": "unit_price", "type": "numeric", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"invoice_items_invoice_id_invoices_id_fk": {"name": "invoice_items_invoice_id_invoices_id_fk", "tableFrom": "invoice_items", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoice_payments": {"name": "invoice_payments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_date": {"name": "payment_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"invoice_payments_invoice_id_invoices_id_fk": {"name": "invoice_payments_invoice_id_invoices_id_fk", "tableFrom": "invoice_payments", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoice_taxes": {"name": "invoice_taxes", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "tax_rate_id": {"name": "tax_rate_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "taxable_amount": {"name": "taxable_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"invoice_taxes_invoice_id_invoices_id_fk": {"name": "invoice_taxes_invoice_id_invoices_id_fk", "tableFrom": "invoice_taxes", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invoice_taxes_tax_rate_id_tax_rates_id_fk": {"name": "invoice_taxes_tax_rate_id_tax_rates_id_fk", "tableFrom": "invoice_taxes", "tableTo": "tax_rates", "columnsFrom": ["tax_rate_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoices": {"name": "invoices", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "number": {"name": "number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "subtotal": {"name": "subtotal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "terms": {"name": "terms", "type": "text", "primaryKey": false, "notNull": false}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"invoices_client_id_clients_id_fk": {"name": "invoices_client_id_clients_id_fk", "tableFrom": "invoices", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invoices_project_id_projects_id_fk": {"name": "invoices_project_id_projects_id_fk", "tableFrom": "invoices", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "invoices_organization_id_organizations_id_fk": {"name": "invoices_organization_id_organizations_id_fk", "tableFrom": "invoices", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invoices_number_unique": {"name": "invoices_number_unique", "nullsNotDistinct": false, "columns": ["number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.late_fees": {"name": "late_fees", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "fee_amount": {"name": "fee_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "applied_date": {"name": "applied_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"late_fees_invoice_id_invoices_id_fk": {"name": "late_fees_invoice_id_invoices_id_fk", "tableFrom": "late_fees", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_invitations": {"name": "organization_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "invited_by_id": {"name": "invited_by_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejected_at": {"name": "rejected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invite_token": {"name": "invite_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_invitations_organization_id_organizations_id_fk": {"name": "organization_invitations_organization_id_organizations_id_fk", "tableFrom": "organization_invitations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_invitations_invited_by_id_users_id_fk": {"name": "organization_invitations_invited_by_id_users_id_fk", "tableFrom": "organization_invitations", "tableTo": "users", "columnsFrom": ["invited_by_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_invitations_invite_token_unique": {"name": "organization_invitations_invite_token_unique", "nullsNotDistinct": false, "columns": ["invite_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_member_permissions": {"name": "organization_member_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_member_id": {"name": "organization_member_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_member_permissions_organization_member_id_organization_members_id_fk": {"name": "organization_member_permissions_organization_member_id_organization_members_id_fk", "tableFrom": "organization_member_permissions", "tableTo": "organization_members", "columnsFrom": ["organization_member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_members": {"name": "organization_members", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organizations_owner_id_users_id_fk": {"name": "organizations_owner_id_users_id_fk", "tableFrom": "organizations", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_gateways": {"name": "payment_gateways", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "config_data": {"name": "config_data", "type": "text", "primaryKey": false, "notNull": false}, "supported_currencies": {"name": "supported_currencies", "type": "text", "primaryKey": false, "notNull": false}, "supported_methods": {"name": "supported_methods", "type": "text", "primaryKey": false, "notNull": false}, "webhook_url": {"name": "webhook_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "webhook_secret": {"name": "webhook_secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"payment_gateways_organization_id_organizations_id_fk": {"name": "payment_gateways_organization_id_organizations_id_fk", "tableFrom": "payment_gateways", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_reminders": {"name": "payment_reminders", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reminder_type": {"name": "reminder_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'sent'"}}, "indexes": {}, "foreignKeys": {"payment_reminders_invoice_id_invoices_id_fk": {"name": "payment_reminders_invoice_id_invoices_id_fk", "tableFrom": "payment_reminders", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_transactions": {"name": "payment_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invoice_id": {"name": "invoice_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "gateway_id": {"name": "gateway_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "external_transaction_id": {"name": "external_transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "gateway_fee": {"name": "gateway_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "net_amount": {"name": "net_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"payment_transactions_invoice_id_invoices_id_fk": {"name": "payment_transactions_invoice_id_invoices_id_fk", "tableFrom": "payment_transactions", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_transactions_gateway_id_payment_gateways_id_fk": {"name": "payment_transactions_gateway_id_payment_gateways_id_fk", "tableFrom": "payment_transactions", "tableTo": "payment_gateways", "columnsFrom": ["gateway_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_comments": {"name": "project_comments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_comments_project_id_projects_id_fk": {"name": "project_comments_project_id_projects_id_fk", "tableFrom": "project_comments", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_comments_user_id_users_id_fk": {"name": "project_comments_user_id_users_id_fk", "tableFrom": "project_comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_comments_parent_id_project_comments_id_fk": {"name": "project_comments_parent_id_project_comments_id_fk", "tableFrom": "project_comments", "tableTo": "project_comments", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_sprints": {"name": "project_sprints", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'planned'"}, "goal": {"name": "goal", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"project_sprints_project_id_projects_id_fk": {"name": "project_sprints_project_id_projects_id_fk", "tableFrom": "project_sprints", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_task_assignees": {"name": "project_task_assignees", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_task_assignees_task_id_project_tasks_id_fk": {"name": "project_task_assignees_task_id_project_tasks_id_fk", "tableFrom": "project_task_assignees", "tableTo": "project_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_task_assignees_user_id_users_id_fk": {"name": "project_task_assignees_user_id_users_id_fk", "tableFrom": "project_task_assignees", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_task_attachments": {"name": "project_task_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_task_attachments_task_id_project_tasks_id_fk": {"name": "project_task_attachments_task_id_project_tasks_id_fk", "tableFrom": "project_task_attachments", "tableTo": "project_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_task_attachments_user_id_users_id_fk": {"name": "project_task_attachments_user_id_users_id_fk", "tableFrom": "project_task_attachments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_task_comments": {"name": "project_task_comments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_task_comments_task_id_project_tasks_id_fk": {"name": "project_task_comments_task_id_project_tasks_id_fk", "tableFrom": "project_task_comments", "tableTo": "project_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_task_comments_user_id_users_id_fk": {"name": "project_task_comments_user_id_users_id_fk", "tableFrom": "project_task_comments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_task_comments_parent_id_project_task_comments_id_fk": {"name": "project_task_comments_parent_id_project_task_comments_id_fk", "tableFrom": "project_task_comments", "tableTo": "project_task_comments", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_tasks": {"name": "project_tasks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "sprint_id": {"name": "sprint_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "parent_task_id": {"name": "parent_task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status_id": {"name": "status_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "estimated_hours": {"name": "estimated_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "actual_hours": {"name": "actual_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"project_tasks_project_id_projects_id_fk": {"name": "project_tasks_project_id_projects_id_fk", "tableFrom": "project_tasks", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_tasks_sprint_id_project_sprints_id_fk": {"name": "project_tasks_sprint_id_project_sprints_id_fk", "tableFrom": "project_tasks", "tableTo": "project_sprints", "columnsFrom": ["sprint_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "project_tasks_parent_task_id_project_tasks_id_fk": {"name": "project_tasks_parent_task_id_project_tasks_id_fk", "tableFrom": "project_tasks", "tableTo": "project_tasks", "columnsFrom": ["parent_task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_tasks_status_id_task_statuses_id_fk": {"name": "project_tasks_status_id_task_statuses_id_fk", "tableFrom": "project_tasks", "tableTo": "task_statuses", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_template_tasks": {"name": "project_template_tasks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "template_id": {"name": "template_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parent_task_id": {"name": "parent_task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "estimated_hours": {"name": "estimated_hours", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "day_offset": {"name": "day_offset", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"project_template_tasks_template_id_project_templates_id_fk": {"name": "project_template_tasks_template_id_project_templates_id_fk", "tableFrom": "project_template_tasks", "tableTo": "project_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_template_tasks_parent_task_id_project_template_tasks_id_fk": {"name": "project_template_tasks_parent_task_id_project_template_tasks_id_fk", "tableFrom": "project_template_tasks", "tableTo": "project_template_tasks", "columnsFrom": ["parent_task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_templates": {"name": "project_templates", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "default_budget": {"name": "default_budget", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "default_currency": {"name": "default_currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"project_templates_organization_id_organizations_id_fk": {"name": "project_templates_organization_id_organizations_id_fk", "tableFrom": "project_templates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "project_templates_created_by_users_id_fk": {"name": "project_templates_created_by_users_id_fk", "tableFrom": "project_templates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "budget": {"name": "budget", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"projects_owner_id_users_id_fk": {"name": "projects_owner_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_organization_id_organizations_id_fk": {"name": "projects_organization_id_organizations_id_fk", "tableFrom": "projects", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_client_id_clients_id_fk": {"name": "projects_client_id_clients_id_fk", "tableFrom": "projects", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposal_approvals": {"name": "proposal_approvals", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "proposal_id": {"name": "proposal_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "approver_user_id": {"name": "approver_user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "approver_email": {"name": "approver_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "approver_name": {"name": "approver_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "comments": {"name": "comments", "type": "text", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"proposal_approvals_proposal_id_proposals_id_fk": {"name": "proposal_approvals_proposal_id_proposals_id_fk", "tableFrom": "proposal_approvals", "tableTo": "proposals", "columnsFrom": ["proposal_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "proposal_approvals_approver_user_id_users_id_fk": {"name": "proposal_approvals_approver_user_id_users_id_fk", "tableFrom": "proposal_approvals", "tableTo": "users", "columnsFrom": ["approver_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposal_items": {"name": "proposal_items", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "proposal_id": {"name": "proposal_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'1'"}, "unit_price": {"name": "unit_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {}, "foreignKeys": {"proposal_items_proposal_id_proposals_id_fk": {"name": "proposal_items_proposal_id_proposals_id_fk", "tableFrom": "proposal_items", "tableTo": "proposals", "columnsFrom": ["proposal_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposals": {"name": "proposals", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "deliverables": {"name": "deliverables", "type": "text", "primaryKey": false, "notNull": false}, "timeline": {"name": "timeline", "type": "text", "primaryKey": false, "notNull": false}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'draft'"}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "responded_at": {"name": "responded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"proposals_client_id_clients_id_fk": {"name": "proposals_client_id_clients_id_fk", "tableFrom": "proposals", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "proposals_organization_id_organizations_id_fk": {"name": "proposals_organization_id_organizations_id_fk", "tableFrom": "proposals", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "proposals_created_by_users_id_fk": {"name": "proposals_created_by_users_id_fk", "tableFrom": "proposals", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.receipts": {"name": "receipts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "vendor_name": {"name": "vendor_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "receipt_date": {"name": "receipt_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"receipts_organization_id_organizations_id_fk": {"name": "receipts_organization_id_organizations_id_fk", "tableFrom": "receipts", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "receipts_category_id_expense_categories_id_fk": {"name": "receipts_category_id_expense_categories_id_fk", "tableFrom": "receipts", "tableTo": "expense_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "receipts_created_by_users_id_fk": {"name": "receipts_created_by_users_id_fk", "tableFrom": "receipts", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_tickets": {"name": "support_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "assigned_to": {"name": "assigned_to", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "closed_at": {"name": "closed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"support_tickets_client_id_clients_id_fk": {"name": "support_tickets_client_id_clients_id_fk", "tableFrom": "support_tickets", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "support_tickets_project_id_projects_id_fk": {"name": "support_tickets_project_id_projects_id_fk", "tableFrom": "support_tickets", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "support_tickets_assigned_to_users_id_fk": {"name": "support_tickets_assigned_to_users_id_fk", "tableFrom": "support_tickets", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "support_tickets_created_by_users_id_fk": {"name": "support_tickets_created_by_users_id_fk", "tableFrom": "support_tickets", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_dependencies": {"name": "task_dependencies", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "depends_on_task_id": {"name": "depends_on_task_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "dependency_type": {"name": "dependency_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'finish_to_start'"}}, "indexes": {}, "foreignKeys": {"task_dependencies_task_id_project_tasks_id_fk": {"name": "task_dependencies_task_id_project_tasks_id_fk", "tableFrom": "task_dependencies", "tableTo": "project_tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_dependencies_depends_on_task_id_project_tasks_id_fk": {"name": "task_dependencies_depends_on_task_id_project_tasks_id_fk", "tableFrom": "task_dependencies", "tableTo": "project_tasks", "columnsFrom": ["depends_on_task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_statuses": {"name": "task_statuses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false, "default": "'#6B7280'"}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"task_statuses_organization_id_organizations_id_fk": {"name": "task_statuses_organization_id_organizations_id_fk", "tableFrom": "task_statuses", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tax_rates": {"name": "tax_rates", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "rate": {"name": "rate", "type": "numeric(5, 4)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "effective_date": {"name": "effective_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"tax_rates_organization_id_organizations_id_fk": {"name": "tax_rates_organization_id_organizations_id_fk", "tableFrom": "tax_rates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_invitations": {"name": "team_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "invited_by_id": {"name": "invited_by_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejected_at": {"name": "rejected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "invite_token": {"name": "invite_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"team_invitations_team_id_teams_id_fk": {"name": "team_invitations_team_id_teams_id_fk", "tableFrom": "team_invitations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "team_invitations_invited_by_id_users_id_fk": {"name": "team_invitations_invited_by_id_users_id_fk", "tableFrom": "team_invitations", "tableTo": "users", "columnsFrom": ["invited_by_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"team_invitations_invite_token_unique": {"name": "team_invitations_invite_token_unique", "nullsNotDistinct": false, "columns": ["invite_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_member_permissions": {"name": "team_member_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "team_member_id": {"name": "team_member_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "permission": {"name": "permission", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"team_member_permissions_team_member_id_team_members_id_fk": {"name": "team_member_permissions_team_member_id_team_members_id_fk", "tableFrom": "team_member_permissions", "tableTo": "team_members", "columnsFrom": ["team_member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"teams_owner_id_users_id_fk": {"name": "teams_owner_id_users_id_fk", "tableFrom": "teams", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ticket_attachments": {"name": "ticket_attachments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ticket_id": {"name": "ticket_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNuld": true}, "file_url": {"name": "file_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"ticket_attachments_ticket_id_support_tickets_id_fk": {"name": "ticket_attachments_ticket_id_support_tickets_id_fk", "tableFrom": "ticket_attachments", "tableTo": "support_tickets", "columnsFrom": ["ticket_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ticket_attachments_message_id_ticket_messages_id_fk": {"name": "ticket_attachments_message_id_ticket_messages_id_fk", "tableFrom": "ticket_attachments", "tableTo": "ticket_messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ticket_messages": {"name": "ticket_messages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ticket_id": {"name": "ticket_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "is_internal": {"name": "is_internal", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"ticket_messages_ticket_id_support_tickets_id_fk": {"name": "ticket_messages_ticket_id_support_tickets_id_fk", "tableFrom": "ticket_messages", "tableTo": "support_tickets", "columnsFrom": ["ticket_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ticket_messages_user_id_users_id_fk": {"name": "ticket_messages_user_id_users_id_fk", "tableFrom": "ticket_messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.two_factors": {"name": "two_factors", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "secret": {"name": "secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "backup_codes": {"name": "backup_codes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"two_factors_user_id_users_id_fk": {"name": "two_factors_user_id_users_id_fk", "tableFrom": "two_factors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_organizations": {"name": "user_organizations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"user_organizations_user_id_users_id_fk": {"name": "user_organizations_user_id_users_id_fk", "tableFrom": "user_organizations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_organizations_organization_id_organizations_id_fk": {"name": "user_organizations_organization_id_organizations_id_fk", "tableFrom": "user_organizations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_username": {"name": "display_username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_type": {"name": "user_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'internal'"}, "client_id": {"name": "client_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_client_id_clients_id_fk": {"name": "users_client_id_clients_id_fk", "tableFrom": "users", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "users_organization_id_organizations_id_fk": {"name": "users_organization_id_organizations_id_fk", "tableFrom": "users", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}