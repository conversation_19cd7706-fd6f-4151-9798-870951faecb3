type AppConfig = {
	databaseUrl: string;
	betterAuthSecret: string;
	betterAuthUrl: string;
};

const config: AppConfig = {
	databaseUrl:
		"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
	betterAuthSecret: "secret",
	betterAuthUrl: "https://better-auth.vercel.app",
};

const getConfigs = (): AppConfig => {
	return config;
};

const getConfig = (key: keyof AppConfig) => {
	return config[key];
};

export { getConfigs, getConfig };
