import { and, eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import type { Permission } from "@/core/auth/permissions";
import type { UserRole } from "@/core/auth/roles";
import { getDb } from "@/core/database";
import { userOrganizations } from "@/core/database/schema";
import { apiInternalError, apiUnauthorized } from "./response";
import type { ApiRequest } from "./types";

interface AuthOptions {
	requireUserType?: "internal" | "client";
	requireOrganization?: boolean;
	requirePermissions?: Permission[];
	requireAnyPermission?: Permission[];
	requireRole?: UserRole;
	allowPublic?: boolean;
}

export async function withApiAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: AuthOptions = {},
) {
	return async (request: NextRequest) => {
		try {
			// Allow public access if specified
			if (options.allowPublic) {
				const context: ApiRequest = {
					user: {
						id: "anonymous",
						email: "<EMAIL>",
						userType: "internal",
						organizationId: undefined,
						clientId: undefined,
						roles: [],
						permissions: [],
					},
					organizationId: "",
				};
				return await handler(request, context);
			}

			// TODO: Implement proper Better Auth integration
			// For now, return unauthorized to test the middleware structure
			return apiUnauthorized("Authentication not yet implemented");
		} catch (error) {
			console.error("API Auth Error:", error);
			return apiInternalError("Authentication failed");
		}
	};
}

// Helper functions to get user roles and permissions
async function _getUserRoles(
	userId: string,
	organizationId?: string,
): Promise<UserRole[]> {
	const db = getDb();

	if (!organizationId) {
		// Return default role for users without organization
		return ["organization_member" as UserRole];
	}

	try {
		// Get user's role in the organization
		const userOrgRecord = await db
			.select()
			.from(userOrganizations)
			.where(
				and(
					eq(userOrganizations.userId, userId),
					eq(userOrganizations.organizationId, organizationId),
					eq(userOrganizations.isActive, true),
				),
			)
			.limit(1);

		if (userOrgRecord.length) {
			return [userOrgRecord[0].role as UserRole];
		}

		return ["organization_member" as UserRole]; // Default role
	} catch (error) {
		console.error("Error fetching user roles:", error);
		return ["organization_member" as UserRole]; // Fallback
	}
}

async function _getUserPermissions(
	_userId: string,
	_roles: UserRole[],
): Promise<Permission[]> {
	// For now, return empty array as custom permissions are not implemented
	// This would query a user_permissions table if custom permissions are needed
	return [];
}

// Convenience middleware functions
export function withInternalAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requireUserType"> = {},
) {
	return withApiAuth(handler, { ...options, requireUserType: "internal" });
}

export function withClientAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requireUserType"> = {},
) {
	return withApiAuth(handler, { ...options, requireUserType: "client" });
}

export function withPermissions(
	permissions: Permission[],
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requirePermissions"> = {},
) {
	return withApiAuth(handler, { ...options, requirePermissions: permissions });
}
