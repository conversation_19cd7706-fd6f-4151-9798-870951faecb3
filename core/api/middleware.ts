import { and, eq } from "drizzle-orm";
import type { NextRequest } from "next/server";
import {
	hasAllPermissions,
	hasAnyPermission,
	type Permission,
	ROLE_PERMISSIONS,
} from "@/core/auth/permissions";
import type { UserRole } from "@/core/auth/roles";
import { auth } from "@/core/auth/server";
import { getDb } from "@/core/database";
import { userOrganizations, users } from "@/core/database/schema";
import { apiForbidden, apiInternalError, apiUnauthorized } from "./response";
import type { ApiRequest } from "./types";

interface AuthOptions {
	requireUserType?: "internal" | "client";
	requireOrganization?: boolean;
	requirePermissions?: Permission[];
	requireAnyPermission?: Permission[];
	requireRole?: UserRole;
	allowPublic?: boolean;
}

export async function withApiAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: AuthOptions = {},
) {
	return async (request: NextRequest) => {
		try {
			// Allow public access if specified
			if (options.allowPublic) {
				const context: ApiRequest = {
					user: {
						id: "anonymous",
						email: "<EMAIL>",
						userType: "internal",
						organizationId: undefined,
						clientId: undefined,
						roles: [],
						permissions: [],
					},
					organizationId: "",
				};
				return await handler(request, context);
			}

			// Get session from Better Auth
			const session = await auth.api.getSession({
				headers: request.headers,
			});

			if (!session) {
				return apiUnauthorized("Authentication required");
			}

			const { user: sessionUser } = session;

			// Get full user details from database
			const db = getDb();
			const userRecord = await db
				.select()
				.from(users)
				.where(eq(users.id, sessionUser.id))
				.limit(1);

			if (!userRecord.length) {
				return apiUnauthorized("User not found");
			}

			const user = userRecord[0];

			// Validate user type if required
			if (
				options.requireUserType &&
				user.userType !== options.requireUserType
			) {
				return apiForbidden(
					`Access denied: ${options.requireUserType} user type required`,
				);
			}

			// Get user roles and organization context
			const userRoles = await getUserRoles(user.id, user.organizationId);
			const userPermissions = getUserPermissions(userRoles);

			// Validate role if required
			if (options.requireRole && !userRoles.includes(options.requireRole)) {
				return apiForbidden(
					`Access denied: ${options.requireRole} role required`,
				);
			}

			// Validate permissions if required
			if (options.requirePermissions?.length) {
				const hasAllRequired = hasAllPermissions(
					userRoles[0], // Primary role
					options.requirePermissions,
					userPermissions,
				);
				if (!hasAllRequired) {
					return apiForbidden("Access denied: insufficient permissions");
				}
			}

			// Validate any permission if required
			if (options.requireAnyPermission?.length) {
				const hasAnyRequired = hasAnyPermission(
					userRoles[0], // Primary role
					options.requireAnyPermission,
					userPermissions,
				);
				if (!hasAnyRequired) {
					return apiForbidden("Access denied: insufficient permissions");
				}
			}

			// Validate organization requirement
			if (options.requireOrganization && !user.organizationId) {
				return apiForbidden("Access denied: organization membership required");
			}

			// Build API request context
			const context: ApiRequest = {
				user: {
					id: user.id,
					email: user.email,
					userType: user.userType as "internal" | "client",
					organizationId: user.organizationId || undefined,
					clientId: user.clientId || undefined,
					roles: userRoles,
					permissions: userPermissions,
				},
				organizationId: user.organizationId || "",
			};

			return await handler(request, context);
		} catch (error) {
			console.error("API Auth Error:", error);
			return apiInternalError("Authentication failed");
		}
	};
}

// Helper functions to get user roles and permissions
async function getUserRoles(
	userId: string,
	organizationId?: string,
): Promise<UserRole[]> {
	const db = getDb();

	if (!organizationId) {
		// Return default role for users without organization
		return ["organization_member" as UserRole];
	}

	try {
		// Get user's role in the organization
		const userOrgRecord = await db
			.select()
			.from(userOrganizations)
			.where(
				and(
					eq(userOrganizations.userId, userId),
					eq(userOrganizations.organizationId, organizationId),
					eq(userOrganizations.isActive, true),
				),
			)
			.limit(1);

		if (userOrgRecord.length) {
			return [userOrgRecord[0].role as UserRole];
		}

		return ["organization_member" as UserRole]; // Default role
	} catch (error) {
		console.error("Error fetching user roles:", error);
		return ["organization_member" as UserRole]; // Fallback
	}
}

function getUserPermissions(roles: UserRole[]): Permission[] {
	// Get permissions from role-based permissions
	const permissions = new Set<Permission>();

	for (const role of roles) {
		const rolePermissions = ROLE_PERMISSIONS[role] || [];
		for (const permission of rolePermissions) {
			permissions.add(permission);
		}
	}

	return Array.from(permissions);
}

// Convenience middleware functions
export function withInternalAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requireUserType"> = {},
) {
	return withApiAuth(handler, { ...options, requireUserType: "internal" });
}

export function withClientAuth(
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requireUserType"> = {},
) {
	return withApiAuth(handler, { ...options, requireUserType: "client" });
}

export function withPermissions(
	permissions: Permission[],
	handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
	options: Omit<AuthOptions, "requirePermissions"> = {},
) {
	return withApiAuth(handler, { ...options, requirePermissions: permissions });
}
