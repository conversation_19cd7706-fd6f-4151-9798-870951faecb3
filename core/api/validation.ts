import type { NextRequest } from "next/server";
import { z } from "zod";
import { apiValidationError } from "./response";
import type { PaginationParams, ValidationError } from "./types";

export async function validateJsonBody<T>(
	request: NextRequest,
	schema: z.ZodSchema<T>,
): Promise<
	{ success: true; data: T } | { success: false; response: Response }
> {
	try {
		const body = await request.json();
		const result = schema.safeParse(body);

		if (!result.success) {
			const errors: ValidationError[] = result.error.errors.map((err) => ({
				field: err.path.join("."),
				message: err.message,
				code: err.code,
			}));

			return {
				success: false,
				response: apiValidationError(errors),
			};
		}

		return {
			success: true,
			data: result.data,
		};
	} catch (_error) {
		return {
			success: false,
			response: apiValidationError([
				{
					field: "body",
					message: "Invalid JSON format",
					code: "invalid_json",
				},
			]),
		};
	}
}

export function parsePaginationParams(
	searchParams: URLSearchParams,
): PaginationParams {
	return {
		page: Math.max(1, parseInt(searchParams.get("page") || "1")),
		limit: Math.min(
			100,
			Math.max(1, parseInt(searchParams.get("limit") || "20")),
		),
		sortBy: searchParams.get("sortBy") || "createdAt",
		sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
	};
}

export function createPaginationMeta(
	page: number,
	limit: number,
	total: number,
) {
	return {
		pagination: {
			page,
			limit,
			total,
			totalPages: Math.ceil(total / limit),
		},
	};
}

// Common validation schemas
export const paginationSchema = z.object({
	page: z.coerce.number().min(1).default(1),
	limit: z.coerce.number().min(1).max(100).default(20),
	sortBy: z.string().default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const idSchema = z.object({
	id: z.string().uuid("Invalid ID format"),
});

export const organizationIdSchema = z.object({
	organizationId: z.string().uuid("Invalid organization ID format"),
});

// Validation helper for URL parameters
export function validateParams<T>(
	params: Record<string, string | string[]>,
	schema: z.ZodSchema<T>,
): { success: true; data: T } | { success: false; errors: ValidationError[] } {
	const result = schema.safeParse(params);

	if (!result.success) {
		const errors: ValidationError[] = result.error.errors.map((err) => ({
			field: err.path.join("."),
			message: err.message,
			code: err.code,
		}));

		return { success: false, errors };
	}

	return { success: true, data: result.data };
}

// Validation helper for search parameters
export function validateSearchParams<T>(
	searchParams: URLSearchParams,
	schema: z.ZodSchema<T>,
): { success: true; data: T } | { success: false; errors: ValidationError[] } {
	const params = Object.fromEntries(searchParams.entries());
	return validateParams(params, schema);
}
