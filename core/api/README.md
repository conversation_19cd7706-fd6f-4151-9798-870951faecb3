# Core API Foundation

This module provides the foundational components for building secure, type-safe APIs in the Freelancer Hub application.

## Features

- **Standardized API Responses**: Consistent response format across all endpoints
- **Authentication Middleware**: Role-based access control with Better Auth integration
- **Validation Helpers**: Zod-based request validation with error handling
- **Type Safety**: Full TypeScript support with no `any` types
- **Permission System**: Simplified permission model with read/manage permissions per module

## Components

### Types (`types.ts`)
- `ApiResponse<T>`: Standardized API response interface
- `ApiRequest`: Authenticated request context
- `PaginationParams`: Pagination parameters interface
- `ValidationError`: Validation error structure

### Response Helpers (`response.ts`)
- `apiSuccess()`: Success response with data
- `apiError()`: Error response with code and message
- `apiValidationError()`: Validation error response
- `apiUnauthorized()`: 401 Unauthorized response
- `apiForbidden()`: 403 Forbidden response
- `apiNotFound()`: 404 Not Found response
- `apiInternalError()`: 500 Internal Server Error response

### Authentication Middleware (`middleware.ts`)
- `withApiAuth()`: General authentication middleware
- `withInternalAuth()`: Internal user authentication
- `withClientAuth()`: Client user authentication
- `withPermissions()`: Permission-based access control

### Validation Helpers (`validation.ts`)
- `validateJsonBody()`: JSON body validation with Zod
- `validateParams()`: URL parameter validation
- `validateSearchParams()`: Query parameter validation
- `parsePaginationParams()`: Pagination parameter parsing
- Common schemas: `paginationSchema`, `idSchema`, `organizationIdSchema`

## Usage Examples

### Basic API Route
```typescript
import { withInternalAuth, apiSuccess, validateJsonBody } from "@/core/api";
import { z } from "zod";

const createSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
});

export const POST = withInternalAuth(
  async (request, context) => {
    const validation = await validateJsonBody(request, createSchema);
    if (!validation.success) {
      return validation.response;
    }

    const { name, description } = validation.data;
    
    // Create resource logic here
    const resource = { id: "123", name, description };
    
    return apiSuccess(resource);
  },
  {
    requirePermissions: [PERMISSIONS.RESOURCE_MANAGE],
    requireOrganization: true,
  }
);
```

### Paginated List Endpoint
```typescript
import { withInternalAuth, apiSuccess, parsePaginationParams, createPaginationMeta } from "@/core/api";

export const GET = withInternalAuth(
  async (request, context) => {
    const { searchParams } = new URL(request.url);
    const { page, limit, sortBy, sortOrder } = parsePaginationParams(searchParams);
    
    // Database query logic here
    const items = [];
    const total = 0;
    
    return apiSuccess(items, createPaginationMeta(page, limit, total));
  },
  {
    requirePermissions: [PERMISSIONS.RESOURCE_READ],
  }
);
```

## Route Structure

### Internal APIs (`/~/api/`)
- Protected routes for internal users (freelancers, team members)
- Require authentication and organization membership
- Use role-based permissions

### Public APIs (`/api/`)
- Public endpoints (health checks, webhooks, auth)
- May allow unauthenticated access
- Limited functionality

## Authentication Flow

1. Request hits API endpoint
2. Middleware extracts session from Better Auth
3. User details fetched from database
4. Roles and permissions validated
5. Request context populated with user info
6. Handler function executed with authenticated context

## Permission System

Each module has exactly two permissions:
- `{module}.read` - View/list resources
- `{module}.manage` - Create/update/delete resources

Example permissions:
- `organization.read` / `organization.manage`
- `project.read` / `project.manage`
- `invoice.read` / `invoice.manage`

## Error Handling

All errors follow a consistent format:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {}
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "uuid"
  }
}
```

## Status

✅ **Implemented**: Core API foundation with authentication, validation, and response helpers
🔄 **In Progress**: Better Auth integration (currently returns unauthorized for testing)
📋 **TODO**: Complete authentication implementation with session management
