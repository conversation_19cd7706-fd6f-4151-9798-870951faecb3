export interface ApiResponse<T = unknown> {
	success: boolean;
	data?: T;
	error?: {
		code: string;
		message: string;
		details?: Record<string, unknown>;
	};
	meta: {
		timestamp: string;
		requestId: string;
		pagination?: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
		};
	};
}

export interface ApiRequest {
	user: {
		id: string;
		email: string;
		userType: "internal" | "client";
		organizationId?: string;
		clientId?: string;
		roles: string[];
		permissions: string[];
	};
	organizationId: string;
}

export interface PaginationParams {
	page: number;
	limit: number;
	sortBy: string;
	sortOrder: "asc" | "desc";
}

export interface ValidationError {
	field: string;
	message: string;
	code: string;
}
