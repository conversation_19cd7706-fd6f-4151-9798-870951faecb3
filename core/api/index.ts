// Core API Foundation - Main exports
export * from "./types";
export * from "./response";
export * from "./middleware";
export * from "./validation";

// Re-export commonly used functions for convenience
export {
  apiSuccess,
  apiError,
  apiValidationError,
  apiUnauthorized,
  apiForbidden,
  apiNotFound,
  apiInternalError,
} from "./response";

export {
  withApiAuth,
  withInternalAuth,
  withClientAuth,
  withPermissions,
} from "./middleware";

export {
  validateJsonBody,
  validateParams,
  validateSearchParams,
  parsePaginationParams,
  createPaginationMeta,
  paginationSchema,
  idSchema,
  organizationIdSchema,
} from "./validation";
