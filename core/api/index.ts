// Core API Foundation - Main exports

export * from "./middleware";
export {
	withApiAuth,
	withClient<PERSON>uth,
	withInternalAuth,
	withPermissions,
} from "./middleware";
export * from "./response";
// Re-export commonly used functions for convenience
export {
	apiError,
	apiForbidden,
	apiInternalError,
	apiNotFound,
	apiSuccess,
	apiUnauthorized,
	apiValidationError,
} from "./response";
export * from "./types";
export * from "./validation";

export {
	createPaginationMeta,
	idSchema,
	organizationIdSchema,
	paginationSchema,
	parsePaginationParams,
	validateJsonBody,
	validateParams,
	validateSearchParams,
} from "./validation";
