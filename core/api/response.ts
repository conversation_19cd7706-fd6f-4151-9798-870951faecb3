import { NextResponse } from "next/server";
import type { ApiResponse, ValidationError } from "./types";

export function apiSuccess<T>(
	data: T,
	meta?: Partial<ApiResponse<T>["meta"]>,
): NextResponse<ApiResponse<T>> {
	return NextResponse.json({
		success: true,
		data,
		meta: {
			timestamp: new Date().toISOString(),
			requestId: crypto.randomUUID(),
			...meta,
		},
	});
}

export function apiError(
	code: string,
	message: string,
	status: number = 400,
	details?: Record<string, unknown>,
): NextResponse<ApiResponse<never>> {
	return NextResponse.json(
		{
			success: false,
			error: {
				code,
				message,
				details,
			},
			meta: {
				timestamp: new Date().toISOString(),
				requestId: crypto.randomUUID(),
			},
		},
		{ status },
	);
}

export function apiValidationError(
	errors: ValidationError[],
): NextResponse<ApiResponse<never>> {
	return apiError("VALIDATION_ERROR", "Validation failed", 400, {
		fields: errors,
	});
}

export function apiUnauthorized(
	message: string = "Authentication required",
): NextResponse<ApiResponse<never>> {
	return apiError("UNAUTHORIZED", message, 401);
}

export function apiForbidden(
	message: string = "Access denied",
): NextResponse<ApiResponse<never>> {
	return apiError("FORBIDDEN", message, 403);
}

export function apiNotFound(
	resource: string = "Resource",
): NextResponse<ApiResponse<never>> {
	return apiError("NOT_FOUND", `${resource} not found`, 404);
}

export function apiInternalError(
	message: string = "Internal server error",
): NextResponse<ApiResponse<never>> {
	return apiError("INTERNAL_ERROR", message, 500);
}
