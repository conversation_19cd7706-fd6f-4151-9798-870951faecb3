import { USER_ROLES, type UserRole } from "./roles";

// Simplified permission system: each module has exactly two permissions
export const PERMISSIONS = {
	// Organization module
	ORGANIZATION_READ: "organization.read",
	ORGANIZATION_MANAGE: "organization.manage",

	// User module
	USER_READ: "user.read",
	USER_MANAGE: "user.manage",

	// Client module
	CLIENT_READ: "client.read",
	CLIENT_MANAGE: "client.manage",

	// Project module
	PROJECT_READ: "project.read",
	PROJECT_MANAGE: "project.manage",

	// Task module
	TASK_READ: "task.read",
	TASK_MANAGE: "task.manage",

	// Invoice module
	INVOICE_READ: "invoice.read",
	INVOICE_MANAGE: "invoice.manage",

	// Financial module
	FINANCIAL_READ: "financial.read",
	FINANCIAL_MANAGE: "financial.manage",

	// System permissions
	SYSTEM_ADMIN: "system.admin",
	CLIENT_PORTAL_ACCESS: "client_portal.access",
} as const;

export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
	[USER_ROLES.SUPER_ADMIN]: [
		PERMISSIONS.SYSTEM_ADMIN,
		PERMISSIONS.ORGANIZATION_MANAGE,
		PERMISSIONS.USER_MANAGE,
		PERMISSIONS.CLIENT_MANAGE,
		PERMISSIONS.PROJECT_MANAGE,
		PERMISSIONS.TASK_MANAGE,
		PERMISSIONS.INVOICE_MANAGE,
		PERMISSIONS.FINANCIAL_MANAGE,
	],

	[USER_ROLES.ORGANIZATION_OWNER]: [
		PERMISSIONS.ORGANIZATION_MANAGE,
		PERMISSIONS.USER_MANAGE,
		PERMISSIONS.CLIENT_MANAGE,
		PERMISSIONS.PROJECT_MANAGE,
		PERMISSIONS.TASK_MANAGE,
		PERMISSIONS.INVOICE_MANAGE,
		PERMISSIONS.FINANCIAL_MANAGE,
	],

	[USER_ROLES.ORGANIZATION_ADMIN]: [
		PERMISSIONS.ORGANIZATION_READ,
		PERMISSIONS.USER_MANAGE,
		PERMISSIONS.CLIENT_MANAGE,
		PERMISSIONS.PROJECT_MANAGE,
		PERMISSIONS.TASK_MANAGE,
		PERMISSIONS.INVOICE_MANAGE,
		PERMISSIONS.FINANCIAL_READ,
	],

	[USER_ROLES.ORGANIZATION_MEMBER]: [
		PERMISSIONS.ORGANIZATION_READ,
		PERMISSIONS.USER_READ,
		PERMISSIONS.CLIENT_READ,
		PERMISSIONS.PROJECT_MANAGE,
		PERMISSIONS.TASK_MANAGE,
		PERMISSIONS.INVOICE_READ,
		PERMISSIONS.FINANCIAL_READ,
	],

	[USER_ROLES.ORGANIZATION_VIEWER]: [
		PERMISSIONS.ORGANIZATION_READ,
		PERMISSIONS.USER_READ,
		PERMISSIONS.CLIENT_READ,
		PERMISSIONS.PROJECT_READ,
		PERMISSIONS.TASK_READ,
		PERMISSIONS.INVOICE_READ,
		PERMISSIONS.FINANCIAL_READ,
	],

	[USER_ROLES.CLIENT_ADMIN]: [
		PERMISSIONS.CLIENT_PORTAL_ACCESS,
		PERMISSIONS.PROJECT_READ,
		PERMISSIONS.TASK_READ,
		PERMISSIONS.INVOICE_MANAGE,
		PERMISSIONS.USER_MANAGE, // Can manage client users
	],

	[USER_ROLES.CLIENT_USER]: [
		PERMISSIONS.CLIENT_PORTAL_ACCESS,
		PERMISSIONS.PROJECT_READ,
		PERMISSIONS.TASK_READ,
		PERMISSIONS.INVOICE_READ,
	],

	[USER_ROLES.CLIENT_VIEWER]: [
		PERMISSIONS.CLIENT_PORTAL_ACCESS,
		PERMISSIONS.PROJECT_READ,
		PERMISSIONS.INVOICE_READ,
	],
};

// Permission checking functions
export function hasPermission(
	userRole: UserRole,
	permission: Permission,
	customPermissions?: Permission[],
): boolean {
	// Check custom permissions first
	if (customPermissions?.includes(permission)) {
		return true;
	}

	// Check role-based permissions
	const rolePermissions = ROLE_PERMISSIONS[userRole];
	return rolePermissions?.includes(permission) || false;
}

export function hasAnyPermission(
	userRole: UserRole,
	permissions: Permission[],
	customPermissions?: Permission[],
): boolean {
	return permissions.some((permission) =>
		hasPermission(userRole, permission, customPermissions),
	);
}

export function hasAllPermissions(
	userRole: UserRole,
	permissions: Permission[],
	customPermissions?: Permission[],
): boolean {
	return permissions.every((permission) =>
		hasPermission(userRole, permission, customPermissions),
	);
}

export function requirePermission(permission: Permission) {
	return (userRole: UserRole, customPermissions?: Permission[]) => {
		if (!hasPermission(userRole, permission, customPermissions)) {
			throw new Error(`Permission denied: ${permission}`);
		}
	};
}

export function getPermissionsForRole(role: UserRole): Permission[] {
	return ROLE_PERMISSIONS[role] || [];
}

export function getPermissionsByModule(module: string): Permission[] {
	return Object.values(PERMISSIONS).filter((permission) =>
		permission.startsWith(`${module}.`),
	);
}
