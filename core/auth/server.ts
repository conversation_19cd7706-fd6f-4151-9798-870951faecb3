import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { getConfig } from "@/core/config";
import { getDb } from "@/core/database";
import {
	accounts,
	sessions,
	twoFactors,
	users,
	verifications,
} from "@/core/database/schema";

export const auth = betterAuth({
	database: drizzleAdapter(getDb(), {
		provider: "pg",
		schema: {
			user: users,
			session: sessions,
			account: accounts,
			verification: verifications,
			twoFactor: twoFactors,
		},
	}),
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: false, // Set to true in production
	},
	session: {
		expiresIn: 60 * 60 * 24 * 7, // 7 days
		updateAge: 60 * 60 * 24, // 1 day
		cookieCache: {
			enabled: true,
			maxAge: 5 * 60, // 5 minutes
		},
	},
	secret: getConfig("betterAuthSecret"),
	baseURL: getConfig("betterAuthUrl"),
	advanced: {
		generateId: () => crypto.randomUUID(),
	},
	trustedOrigins: [getConfig("betterAuthUrl")],
});
