# Task 2: Environment Configuration and Deployment Setup

## Task Title

Configure environment variables, secrets management, and deployment pipeline for Cloudflare Workers

## Context

Proper environment configuration is critical for the platform's security and functionality. This task establishes secure management of sensitive data like database credentials, authentication secrets, and API keys while ensuring the application can be deployed to Cloudflare Workers with the correct configuration.

The configuration must support multiple environments (development, staging, production) and integrate with Cloudflare's secrets management system for production deployments.

## Development Guidelines

**Configuration Management:**

- All environment variables must be documented and validated through the centralized configuration system
- Use `.env.example` to document all required environment variables
- Never commit actual environment values to version control

**TypeScript Standards:**

- Environment variable validation must use proper TypeScript types
- No `any` types in environment configuration or validation
- Use Zod schemas for runtime validation of environment variables

**Security Standards:**

- Implement proper secret management for production environments
- Use different configurations for development, staging, and production
- Ensure sensitive data is properly encrypted and secured

**Documentation Requirements:**

- Before configuring deployment environments, use Context7 MCP tool for latest Cloudflare Workers documentation
- Document all environment setup procedures and deployment steps
- Provide clear instructions for local development setup

**Deployment Standards:**

- Support multiple environments (development, staging, production)
- Implement proper CI/CD environment variable management
- Ensure configuration validation prevents deployment with invalid settings

## Acceptance Criteria

1. **Environment Variables**

   - All required environment variables are documented
   - Development environment uses `.env.local` file
   - Production uses Cloudflare Workers secrets
   - Environment validation prevents startup with missing variables

2. **Security Configuration**

   - Database credentials are properly secured
   - Authentication secrets are randomly generated and secure
   - API keys and sensitive data are not exposed in client-side code
   - Environment-specific configurations are properly isolated

3. **Deployment Configuration**

   - Cloudflare Workers deployment is configured
   - Wrangler configuration supports multiple environments
   - Build process includes environment validation
   - Database migrations can run in production

4. **Development Setup**
   - Local development environment is fully functional
   - Hot reloading works with environment changes
   - Database connection works in development
   - Authentication flows work locally

## Dependencies

- Task 1: Database Schema Implementation
- Task 2: Better Auth Configuration
- Cloudflare Workers account and setup
- Neon PostgreSQL database instance

## Technical Requirements

### Environment Variables Documentation

**File: `.env.example`**

```env
# Database Configuration
DATABASE_URL=****************************************/database
DATABASE_POOL_SIZE=10

# Authentication
BETTER_AUTH_SECRET=your-32-character-secret-key-here
BETTER_AUTH_URL=http://localhost:3000
NEXT_PUBLIC_BETTER_AUTH_URL=http://localhost:3000

# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# Email Configuration (for future use)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# File Upload (for future use)
CLOUDFLARE_R2_ACCOUNT_ID=your-account-id
CLOUDFLARE_R2_ACCESS_KEY_ID=your-access-key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-secret-key
CLOUDFLARE_R2_BUCKET_NAME=your-bucket-name

# Analytics (for future use)
ANALYTICS_API_KEY=your-analytics-key

# Rate Limiting
UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token
```

### Environment Validation

**File: `core/config/env.ts`**

```typescript
import { z } from "zod";

const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url("Invalid database URL"),
  DATABASE_POOL_SIZE: z.coerce.number().min(1).max(50).default(10),

  // Authentication
  BETTER_AUTH_SECRET: z
    .string()
    .min(32, "Auth secret must be at least 32 characters"),
  BETTER_AUTH_URL: z.string().url("Invalid auth URL"),

  // Application
  NODE_ENV: z
    .enum(["development", "staging", "production"])
    .default("development"),
  NEXT_PUBLIC_APP_URL: z.string().url("Invalid app URL"),
  NEXT_PUBLIC_API_URL: z.string().url("Invalid API URL"),

  // Optional - Email
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.coerce.number().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  SMTP_FROM: z.string().email().optional(),

  // Optional - File Upload
  CLOUDFLARE_R2_ACCOUNT_ID: z.string().optional(),
  CLOUDFLARE_R2_ACCESS_KEY_ID: z.string().optional(),
  CLOUDFLARE_R2_SECRET_ACCESS_KEY: z.string().optional(),
  CLOUDFLARE_R2_BUCKET_NAME: z.string().optional(),

  // Optional - Rate Limiting
  UPSTASH_REDIS_REST_URL: z.string().url().optional(),
  UPSTASH_REDIS_REST_TOKEN: z.string().optional(),
});

export type Env = z.infer<typeof envSchema>;

let env: Env;

export function getEnv(): Env {
  if (!env) {
    const result = envSchema.safeParse(process.env);

    if (!result.success) {
      console.error("❌ Invalid environment variables:");
      console.error(result.error.flatten().fieldErrors);
      throw new Error("Invalid environment configuration");
    }

    env = result.data;
    console.log("✅ Environment variables validated successfully");
  }

  return env;
}

// Validate environment on module load
getEnv();
```

### Cloudflare Workers Configuration

**File: `wrangler.toml`**

```toml
name = "freelancer-hub"
main = "dist/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.development]
name = "freelancer-hub-dev"
vars = { NODE_ENV = "development" }

[env.staging]
name = "freelancer-hub-staging"
vars = { NODE_ENV = "staging" }

[env.production]
name = "freelancer-hub-prod"
vars = { NODE_ENV = "production" }

# Build configuration
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

# Database bindings will be configured via secrets
```

### Secrets Management Script

**File: `scripts/setup-secrets.sh`**

```bash
#!/bin/bash

# Script to set up Cloudflare Workers secrets
# Usage: ./scripts/setup-secrets.sh [environment]

ENVIRONMENT=${1:-production}

echo "Setting up secrets for environment: $ENVIRONMENT"

# Check if required environment variables are set
if [ -z "$DATABASE_URL" ]; then
  echo "Error: DATABASE_URL environment variable is required"
  exit 1
fi

if [ -z "$BETTER_AUTH_SECRET" ]; then
  echo "Error: BETTER_AUTH_SECRET environment variable is required"
  exit 1
fi

# Set secrets in Cloudflare Workers
echo "Setting DATABASE_URL..."
echo "$DATABASE_URL" | wrangler secret put DATABASE_URL --env $ENVIRONMENT

echo "Setting BETTER_AUTH_SECRET..."
echo "$BETTER_AUTH_SECRET" | wrangler secret put BETTER_AUTH_SECRET --env $ENVIRONMENT

echo "Setting BETTER_AUTH_URL..."
if [ "$ENVIRONMENT" = "production" ]; then
  echo "https://your-domain.com" | wrangler secret put BETTER_AUTH_URL --env $ENVIRONMENT
elif [ "$ENVIRONMENT" = "staging" ]; then
  echo "https://staging.your-domain.com" | wrangler secret put BETTER_AUTH_URL --env $ENVIRONMENT
else
  echo "https://dev.your-domain.com" | wrangler secret put BETTER_AUTH_URL --env $ENVIRONMENT
fi

echo "✅ Secrets configured successfully for $ENVIRONMENT"
```

### Database Configuration

**File: `core/database/config.ts`**

```typescript
import { getEnv } from "@/core/config/env";
import { neon } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import * as schema from "./schema";

let db: ReturnType<typeof drizzle>;

export function getDb() {
  if (!db) {
    const env = getEnv();
    const sql = neon(env.DATABASE_URL);
    db = drizzle(sql, { schema });
  }
  return db;
}

export function getDatabaseConfig() {
  const env = getEnv();
  return {
    url: env.DATABASE_URL,
    poolSize: env.DATABASE_POOL_SIZE,
  };
}
```

### Development Setup Script

**File: `scripts/dev-setup.sh`**

```bash
#!/bin/bash

echo "🚀 Setting up development environment..."

# Check if .env.local exists
if [ ! -f .env.local ]; then
  echo "📝 Creating .env.local from .env.example..."
  cp .env.example .env.local
  echo "⚠️  Please update .env.local with your actual values"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate database schema
echo "🗄️  Generating database schema..."
npx drizzle-kit generate

# Run database migrations
echo "🔄 Running database migrations..."
npx drizzle-kit migrate

# Validate environment
echo "✅ Validating environment configuration..."
npm run validate-env

echo "🎉 Development environment setup complete!"
echo "Run 'npm run dev' to start the development server"
```

### Package.json Scripts

**File: `package.json` (scripts section)**

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "validate-env": "node -e \"require('./core/config/env').getEnv()\"",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit migrate",
    "db:studio": "drizzle-kit studio",
    "deploy:dev": "wrangler deploy --env development",
    "deploy:staging": "wrangler deploy --env staging",
    "deploy:prod": "wrangler deploy --env production",
    "setup:dev": "./scripts/dev-setup.sh",
    "setup:secrets": "./scripts/setup-secrets.sh"
  }
}
```

### Environment-Specific Configuration

**File: `core/config/index.ts`**

```typescript
import { getEnv } from "./env";

export function getConfig() {
  const env = getEnv();

  return {
    app: {
      name: "Freelancer Hub",
      version: "1.0.0",
      url: env.NEXT_PUBLIC_APP_URL,
      apiUrl: env.NEXT_PUBLIC_API_URL,
    },
    auth: {
      secret: env.BETTER_AUTH_SECRET,
      url: env.BETTER_AUTH_URL,
      sessionDuration: 7 * 24 * 60 * 60, // 7 days in seconds
    },
    database: {
      url: env.DATABASE_URL,
      poolSize: env.DATABASE_POOL_SIZE,
    },
    email: env.SMTP_HOST
      ? {
          host: env.SMTP_HOST,
          port: env.SMTP_PORT!,
          user: env.SMTP_USER!,
          pass: env.SMTP_PASS!,
          from: env.SMTP_FROM!,
        }
      : null,
    storage: env.CLOUDFLARE_R2_ACCOUNT_ID
      ? {
          accountId: env.CLOUDFLARE_R2_ACCOUNT_ID,
          accessKeyId: env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
          secretAccessKey: env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
          bucketName: env.CLOUDFLARE_R2_BUCKET_NAME!,
        }
      : null,
    redis: env.UPSTASH_REDIS_REST_URL
      ? {
          url: env.UPSTASH_REDIS_REST_URL,
          token: env.UPSTASH_REDIS_REST_TOKEN!,
        }
      : null,
    isDevelopment: env.NODE_ENV === "development",
    isProduction: env.NODE_ENV === "production",
  };
}
```

## Definition of Done

- [ ] All required environment variables are documented in `.env.example`
- [ ] Environment validation prevents startup with missing variables
- [ ] Local development uses `.env.local` file
- [ ] Cloudflare Workers configuration supports multiple environments
- [ ] Secrets management script configures production secrets
- [ ] Database connection works in all environments
- [ ] Authentication configuration is environment-aware
- [ ] Development setup script automates local setup
- [ ] Build process validates environment configuration
- [ ] Deployment scripts work for all environments
- [ ] Environment-specific configurations are properly isolated
- [ ] Sensitive data is not exposed in client-side code
- [ ] Hot reloading works with environment changes
- [ ] Database migrations can run in production
- [ ] Configuration is type-safe with Zod validation
