# Task 1: Database Schema Implementation

## Task Title

Implement and deploy the comprehensive database schema for the freelancer management platform

## Context

The database schema is the foundation of the entire platform. We have already designed a comprehensive schema with 46+ interconnected tables supporting multi-tenant architecture, but we need to implement the actual database migrations and ensure the schema is properly deployed to our Neon PostgreSQL instance.

This task establishes the data layer that will support all subsequent features including authentication, project management, client relationships, and financial tracking.

## Development Guidelines

**Configuration Management:**

- All database configuration must be accessed through `/core/config.ts` - never read environment variables directly
- Use `getDatabaseConfig()` helper function for database-specific settings
- Database connection pooling and settings must be centralized

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in schema definitions
- Use proper Drizzle ORM types and interfaces for all database operations
- Implement proper type guards for database query results

**Documentation Requirements:**

- Before implementing database operations, use Context7 MCP tool to retrieve latest Drizzle ORM documentation
- Ensure migration scripts follow current Drizzle best practices
- Document all schema changes and migration procedures

**Database Standards:**

- All tables must include proper foreign key constraints with appropriate cascade rules
- Use consistent naming conventions (snake_case for database, camelCase for TypeScript)
- Implement proper indexing for multi-tenant queries and performance optimization
- Include audit fields (createdAt, updatedAt) on all business tables

## Acceptance Criteria

1. **Schema Deployment**

   - All 46+ tables are created in the Neon PostgreSQL database
   - Foreign key relationships are properly established
   - Indexes are created for performance optimization
   - Database migrations run successfully

2. **Data Integrity**

   - All foreign key constraints are enforced
   - Unique constraints prevent duplicate data
   - Default values are properly set
   - Cascade deletes work correctly for dependent data

3. **Multi-tenant Support**

   - Organization-based data isolation is enforced
   - All business tables include organizationId references
   - Row-level security foundations are in place

4. **Performance Optimization**
   - Critical indexes are created for query performance
   - Composite indexes support multi-tenant queries
   - Database connection pooling is configured

## Dependencies

- Neon PostgreSQL database instance
- Drizzle ORM configuration
- Database connection string in environment variables

## Technical Requirements

### Database Tables to Implement

**Core Authentication (Better Auth)**

- `users` - Unified user table with userType field
- `sessions` - User sessions
- `accounts` - OAuth and password providers
- `verifications` - Email/phone verification
- `twoFactors` - 2FA authentication

**Organization & Team Management**

- `organizations` - Multi-tenant root entities
- `organizationMembers` - Organization membership
- `organizationInvitations` - Organization invites
- `organizationMemberPermissions` - RBAC permissions
- `teams` - Team structure within organizations
- `teamMembers` - Team membership
- `teamInvitations` - Team invites
- `teamMemberPermissions` - Team-level permissions

**Client Management**

- `clients` - Client information
- `clientBillingAddress` - Client billing addresses
- `clientContacts` - Multiple contact persons per client
- `clientPreferences` - Communication preferences
- `clientNotifications` - Client notifications

**Project & Task Management**

- `projects` - Project structure
- `projectSprints` - Sprint management
- `projectTasks` - Hierarchical task structure
- `projectTaskAssignees` - Task assignments
- `projectTaskComments` - Task commenting
- `projectTaskAttachments` - File attachments
- `taskStatuses` - Custom task statuses per organization
- `taskDependencies` - Task dependencies
- `projectComments` - Project-level comments

**Financial Management**

- `invoices` - Invoice tracking
- `invoiceItems` - Invoice line items
- `invoicePayments` - Payment tracking
- `paymentReminders` - Automated reminders
- `lateFees` - Late fee management
- `receipts` - Expense tracking
- `expenseCategories` - Expense categorization

**Contract & Document Management**

- `contractTemplates` - Reusable contract templates
- `contracts` - Client contracts
- `contractSignatures` - Digital signatures
- `documentTemplates` - PDF generation templates
- `generatedDocuments` - Generated documents

**Support & Communication**

- `supportTickets` - Client support system
- `ticketMessages` - Ticket replies
- `ticketAttachments` - Ticket attachments

**Integration & File Management**

- `integrations` - Third-party integrations
- `clientIntegrationAccess` - Client integration access
- `fileUploads` - Centralized file management

**Audit & Activity**

- `activityLogs` - Comprehensive audit trails

### Database Migrations

1. **Create Migration Files**

   ```bash
   npx drizzle-kit generate
   ```

2. **Apply Migrations**

   ```bash
   npx drizzle-kit push
   ```

3. **Verify Schema**
   ```bash
   npx drizzle-kit introspect
   ```

### Critical Indexes

```sql
-- Multi-tenant data isolation
CREATE INDEX idx_projects_org_client ON projects(organization_id, client_id);
CREATE INDEX idx_tasks_project_status ON project_tasks(project_id, status_id);
CREATE INDEX idx_invoices_client_status ON invoices(client_id, status);

-- Performance optimization
CREATE INDEX idx_users_org_type ON users(organization_id, user_type);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_sessions_user_expires ON sessions(user_id, expires_at);
```

### Environment Configuration

Required environment variables:

- `DATABASE_URL` - Neon PostgreSQL connection string
- `BETTER_AUTH_SECRET` - Authentication secret key
- `BETTER_AUTH_URL` - Base URL for authentication

## Definition of Done

- [ ] All database tables are created and accessible
- [ ] Foreign key relationships are properly established
- [ ] Database migrations run without errors
- [ ] Critical indexes are created for performance
- [ ] Database connection is properly configured in the application
- [ ] Schema introspection confirms all tables and relationships
- [ ] Multi-tenant data isolation is verified through test queries
- [ ] Database backup and recovery procedures are documented
- [ ] Connection pooling is configured for production use
- [ ] Database monitoring and logging are set up
