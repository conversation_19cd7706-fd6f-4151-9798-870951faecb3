# Task 5: Role-Based Access Control System

## Task Title

Implement simplified role and permission system for multi-tenant access control

## Context

Following the platform development guidelines, all role definitions must be stored in `/core/auth/roles.ts` and all permission logic in `/core/auth/permissions.ts`. This task establishes a simplified RBAC system that differentiates between internal users (freelancers, team members) and client users while supporting the multi-tenant architecture.

The system uses a simplified two-permission model per module (read/manage) that provides clear access control while remaining easy to understand and maintain. This foundation will be used by all API endpoints and UI components to enforce proper authorization.

**Modular Architecture Requirements:**

- Each functional module (organization, user, client, project, task, invoice, financial) has exactly two permissions: `{module}.read` and `{module}.manage`
- Permission system is self-contained in `/core/auth/` directory
- Clear separation between role definitions and permission logic
- Module-based permission helpers for easy integration

## Development Guidelines

**Configuration Management:**

- Access configuration through `/core/config.ts` using centralized configuration system
- Never read environment variables directly in role/permission logic
- Use configuration helpers for any auth-related settings

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in role/permission definitions
- Use proper TypeScript enums and const assertions for role and permission types
- Implement proper type guards for role and permission validation

**Route Structure:**

- Role/permission middleware must support `/~/api` route protection for internal APIs
- Client role validation must work with client portal routes
- Ensure proper separation between internal and client user authorization

**Documentation Requirements:**

- Before implementing RBAC features, use Context7 MCP tool to retrieve latest authorization best practices
- Document all roles, permissions, and their intended use cases
- Provide clear examples of how to use the permission system

**Security Standards:**

- Implement principle of least privilege for all role definitions
- Ensure proper role hierarchy and permission inheritance
- Validate all permission checks are secure and cannot be bypassed

**Modular Architecture:**

- Each module has self-contained permission definitions
- Permission helpers are organized by functional area
- Clear separation between role definitions and permission mappings
- Module-based access control for scalable authorization

## Acceptance Criteria

1. **Simplified Permission Model**

   - Each module has exactly two permissions: `{module}.read` and `{module}.manage`
   - Clear permission hierarchy where manage includes read access
   - Type-safe permission definitions with TypeScript
   - Module-based permission organization

2. **Role Definition System**

   - Clear separation between internal and client user roles
   - Role hierarchy with inheritance and permission levels
   - Extensible role system for future requirements
   - System roles protected from modification

3. **Permission Management**

   - Simplified two-permission model for all platform modules
   - Role-to-permission mapping with clear inheritance
   - Module-based permission helpers for easy integration
   - Permission validation and checking utilities

4. **Multi-Tenant Support**
   - Organization-specific role assignments
   - Cross-organization permission isolation
   - Client-specific access controls
   - Proper data isolation enforcement

## Dependencies

- Task 3: Centralized Configuration System
- Task 4: Better Auth Configuration
- Multi-tenant database schema
- TypeScript strict mode enabled

## Technical Requirements

### Module Structure

```
core/
└── auth/
    ├── roles.ts              # Role definitions and utilities
    ├── permissions.ts        # Permission definitions and helpers
    └── middleware.ts         # Authorization middleware (from Task 6)
```

### Role Definition System

**File: `core/auth/roles.ts`**

```typescript
export const USER_ROLES = {
  // Internal user roles (freelancers, team members)
  SUPER_ADMIN: "super_admin",
  ORGANIZATION_OWNER: "organization_owner",
  ORGANIZATION_ADMIN: "organization_admin",
  ORGANIZATION_MEMBER: "organization_member",
  ORGANIZATION_VIEWER: "organization_viewer",

  // Client user roles (client portal access)
  CLIENT_ADMIN: "client_admin",
  CLIENT_USER: "client_user",
  CLIENT_VIEWER: "client_viewer",
} as const;

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

export interface RoleDefinition {
  name: string;
  description: string;
  userType: "internal" | "client";
  level: number; // Higher number = more permissions
  inherits?: UserRole[];
  isSystemRole?: boolean; // Cannot be deleted or modified
}

export const ROLE_DEFINITIONS: Record<UserRole, RoleDefinition> = {
  [USER_ROLES.SUPER_ADMIN]: {
    name: "Super Admin",
    description: "Platform-wide administrative access",
    userType: "internal",
    level: 100,
    isSystemRole: true,
  },

  [USER_ROLES.ORGANIZATION_OWNER]: {
    name: "Organization Owner",
    description: "Full control over organization and all its data",
    userType: "internal",
    level: 90,
    isSystemRole: true,
  },

  [USER_ROLES.ORGANIZATION_ADMIN]: {
    name: "Organization Admin",
    description: "Administrative access within organization",
    userType: "internal",
    level: 80,
    isSystemRole: true,
  },

  [USER_ROLES.ORGANIZATION_MEMBER]: {
    name: "Organization Member",
    description: "Standard member access within organization",
    userType: "internal",
    level: 60,
    isSystemRole: true,
  },

  [USER_ROLES.ORGANIZATION_VIEWER]: {
    name: "Organization Viewer",
    description: "Read-only access within organization",
    userType: "internal",
    level: 40,
    isSystemRole: true,
  },

  [USER_ROLES.CLIENT_ADMIN]: {
    name: "Client Admin",
    description: "Administrative access to client portal",
    userType: "client",
    level: 70,
    isSystemRole: true,
  },

  [USER_ROLES.CLIENT_USER]: {
    name: "Client User",
    description: "Standard client portal access",
    userType: "client",
    level: 50,
    isSystemRole: true,
  },

  [USER_ROLES.CLIENT_VIEWER]: {
    name: "Client Viewer",
    description: "Read-only client portal access",
    userType: "client",
    level: 30,
    isSystemRole: true,
  },
};

// Role utility functions
export function getRoleDefinition(role: UserRole): RoleDefinition {
  return ROLE_DEFINITIONS[role];
}

export function isInternalRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "internal";
}

export function isClientRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "client";
}

export function hasHigherRole(
  userRole: UserRole,
  requiredRole: UserRole
): boolean {
  return (
    ROLE_DEFINITIONS[userRole].level >= ROLE_DEFINITIONS[requiredRole].level
  );
}

export function getRolesByUserType(
  userType: "internal" | "client"
): UserRole[] {
  return Object.keys(ROLE_DEFINITIONS).filter(
    (role) => ROLE_DEFINITIONS[role as UserRole].userType === userType
  ) as UserRole[];
}

export function getInternalRoles(): UserRole[] {
  return getRolesByUserType("internal");
}

export function getClientRoles(): UserRole[] {
  return getRolesByUserType("client");
}
```

### Simplified Permission System

**File: `core/auth/permissions.ts`**

```typescript
import { UserRole, USER_ROLES, ROLE_DEFINITIONS } from "./roles";

// Simplified two-permission model per module
export const PERMISSIONS = {
  // Organization permissions
  ORGANIZATION_READ: "organization.read",
  ORGANIZATION_MANAGE: "organization.manage",

  // User permissions
  USER_READ: "user.read",
  USER_MANAGE: "user.manage",

  // Client permissions
  CLIENT_READ: "client.read",
  CLIENT_MANAGE: "client.manage",

  // Project permissions
  PROJECT_READ: "project.read",
  PROJECT_MANAGE: "project.manage",

  // Task permissions
  TASK_READ: "task.read",
  TASK_MANAGE: "task.manage",

  // Invoice permissions
  INVOICE_READ: "invoice.read",
  INVOICE_MANAGE: "invoice.manage",

  // Financial permissions
  FINANCIAL_READ: "financial.read",
  FINANCIAL_MANAGE: "financial.manage",
} as const;

export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];

// Simplified role-to-permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [USER_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),

  [USER_ROLES.ORGANIZATION_OWNER]: [
    PERMISSIONS.ORGANIZATION_MANAGE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.CLIENT_MANAGE,
    PERMISSIONS.PROJECT_MANAGE,
    PERMISSIONS.TASK_MANAGE,
    PERMISSIONS.INVOICE_MANAGE,
    PERMISSIONS.FINANCIAL_MANAGE,
  ],

  [USER_ROLES.ORGANIZATION_ADMIN]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.CLIENT_MANAGE,
    PERMISSIONS.PROJECT_MANAGE,
    PERMISSIONS.TASK_MANAGE,
    PERMISSIONS.INVOICE_MANAGE,
    PERMISSIONS.FINANCIAL_MANAGE,
  ],

  [USER_ROLES.ORGANIZATION_MEMBER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_MANAGE,
    PERMISSIONS.TASK_MANAGE,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],

  [USER_ROLES.ORGANIZATION_VIEWER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],

  [USER_ROLES.CLIENT_ADMIN]: [
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.USER_READ,
  ],

  [USER_ROLES.CLIENT_USER]: [
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
  ],

  [USER_ROLES.CLIENT_VIEWER]: [
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.INVOICE_READ,
  ],
};

// Permission checking functions
export function hasPermission(
  userRole: UserRole,
  permission: Permission,
  customPermissions?: Permission[]
): boolean {
  // Check custom permissions first
  if (customPermissions?.includes(permission)) {
    return true;
  }

  // Check role-based permissions
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  return rolePermissions?.includes(permission) || false;
}

export function hasAnyPermission(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.some((permission) =>
    hasPermission(userRole, permission, customPermissions)
  );
}

export function hasAllPermissions(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.every((permission) =>
    hasPermission(userRole, permission, customPermissions)
  );
}

export function requirePermission(permission: Permission) {
  return (userRole: UserRole, customPermissions?: Permission[]) => {
    if (!hasPermission(userRole, permission, customPermissions)) {
      throw new Error(`Permission denied: ${permission}`);
    }
  };
}

export function getPermissionsForRole(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

export function getPermissionsByModule(module: string): Permission[] {
  return Object.values(PERMISSIONS).filter((permission) =>
    permission.startsWith(`${module}.`)
  );
}

// Helper to check if user has read access to a module
export function hasModuleReadAccess(
  userRole: UserRole,
  module: string,
  customPermissions?: Permission[]
): boolean {
  const readPermission = `${module}.read` as Permission;
  const managePermission = `${module}.manage` as Permission;

  return (
    hasPermission(userRole, readPermission, customPermissions) ||
    hasPermission(userRole, managePermission, customPermissions)
  );
}

// Helper to check if user has manage access to a module
export function hasModuleManageAccess(
  userRole: UserRole,
  module: string,
  customPermissions?: Permission[]
): boolean {
  const managePermission = `${module}.manage` as Permission;
  return hasPermission(userRole, managePermission, customPermissions);
}
```

## Definition of Done

- [ ] Simplified permission system with two permissions per module is implemented
- [ ] Role definition system is implemented in `/core/auth/roles.ts`
- [ ] Permission system is implemented in `/core/auth/permissions.ts`
- [ ] Clear separation between internal and client user roles
- [ ] Role hierarchy with proper permission levels is functional
- [ ] Permission checking utilities work correctly
- [ ] Type-safe role and permission definitions with TypeScript
- [ ] Role-to-permission mapping uses simplified permissions
- [ ] Multi-tenant support with organization-specific roles
- [ ] Module-based permission helpers are available
- [ ] Role validation and comparison functions work correctly
- [ ] Permission inheritance follows manage-includes-read pattern
- [ ] System roles are protected from modification
- [ ] Documentation explains simplified permission model
- [ ] Module-based access control is clearly defined
