# Task 3: Centralized Configuration System

## Task Title

Implement centralized configuration management with type-safe environment validation

## Context

Following the platform development guidelines, all configuration must be centralized in `/core/config.ts` to prevent direct environment variable access throughout the codebase. This task establishes a type-safe configuration system that validates all environment variables at startup and provides a single source of truth for application settings.

This centralized approach ensures consistency, prevents configuration errors, and makes it easier to manage different environments (development, staging, production) while maintaining strict TypeScript typing throughout the application.

## Development Guidelines

**Configuration Management:**

- This task IS the centralized configuration system - all other tasks will depend on this
- Implement strict validation using Zod schemas for all environment variables
- Provide helper functions for accessing specific configuration sections

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in configuration schemas
- Use proper Zod types and TypeScript inference for all configuration objects
- Implement proper type guards for configuration validation

**Documentation Requirements:**

- Before implementing configuration validation, use Context7 MCP tool to retrieve latest Zod documentation
- Ensure configuration follows current validation best practices
- Document all configuration options and their purposes

**Security Standards:**

- Never log sensitive configuration values (secrets, API keys)
- Implement proper environment-specific configuration loading
- Ensure configuration validation fails fast on startup with invalid settings

**Integration Standards:**

- Provide typed helper functions for common configuration access patterns
- Support environment-specific overrides and defaults
- Ensure configuration system works seamlessly with Cloudflare Workers environment

## Acceptance Criteria

1. **API Structure**

   - Internal API routes organized under `/~/api/` prefix
   - Public API routes organized under `/api/` prefix (webhooks, auth callbacks)
   - Standardized request/response patterns with proper TypeScript interfaces
   - API versioning support for future compatibility

2. **Authentication Integration**

   - All protected endpoints validate user sessions through centralized middleware
   - User context is available in all API handlers with proper typing
   - Organization-based data isolation is enforced
   - Role-based access control using `/core/auth/roles.ts` and `/core/auth/permissions.ts`

3. **Error Handling**

   - Centralized error handling with consistent error responses
   - Proper logging of API errors and activities
   - Validation errors are properly formatted with TypeScript interfaces
   - Database errors are handled gracefully

4. **Response Patterns**
   - Consistent JSON response structure with TypeScript interfaces
   - Pagination support for list endpoints
   - Metadata included in responses (timestamps, counts, etc.)
   - Success and error responses follow the same pattern

## Dependencies

- Task 1: Database Schema Implementation
- Task 2: Environment Configuration and Deployment Setup
- Zod validation library
- TypeScript strict mode enabled

## Technical Requirements

### API Route Structure

```
app/
├── ~/
│   └── api/                    # Internal APIs (dashboard/admin)
│       ├── organizations/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── users/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── clients/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── projects/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       └── invoices/
│           ├── route.ts
│           └── [id]/route.ts
└── api/                        # Public APIs
    ├── auth/                   # Better Auth endpoints
    │   └── [...auth]/route.ts
    ├── webhooks/               # External webhooks
    │   ├── stripe/route.ts
    │   └── resend/route.ts
    └── health/route.ts         # Public health check
```

**Note**: Centralized configuration, role/permission system, and email integration will be implemented in separate dedicated tasks to maintain clear separation of concerns.
console.error(result.error.flatten().fieldErrors);
throw new Error("Invalid configuration");
}

    config = result.data;
    console.log("✅ Configuration loaded successfully");

}

return config;
}

````
`

### Core API Types

**File: `core/api/types.ts`**
```typescript
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  meta: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiRequest {
  user: {
    id: string;
    email: string;
    userType: "internal" | "client";
    organizationId?: string;
    clientId?: string;
    roles: string[];
    permissions: string[];
  };
  organizationId: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}
````

`

### Role and Permission System

**File: `core/auth/roles.ts`**

```typescript
export const USER_ROLES = {
  // Internal user roles
  SUPER_ADMIN: "super_admin",
  ORGANIZATION_OWNER: "organization_owner",
  ORGANIZATION_ADMIN: "organization_admin",
  ORGANIZATION_MEMBER: "organization_member",
  ORGANIZATION_VIEWER: "organization_viewer",

  // Client user roles
  CLIENT_ADMIN: "client_admin",
  CLIENT_USER: "client_user",
  CLIENT_VIEWER: "client_viewer",
} as const;

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

export interface RoleDefinition {
  name: string;
  description: string;
  userType: "internal" | "client";
  level: number; // Higher number = more permissions
  inherits?: UserRole[];
}

export const ROLE_DEFINITIONS: Record<UserRole, RoleDefinition> = {
  [USER_ROLES.SUPER_ADMIN]: {
    name: "Super Admin",
    description: "Platform-wide administrative access",
    userType: "internal",
    level: 100,
  },

  [USER_ROLES.ORGANIZATION_OWNER]: {
    name: "Organization Owner",
    description: "Full control over organization and all its data",
    userType: "internal",
    level: 90,
  },

  [USER_ROLES.ORGANIZATION_ADMIN]: {
    name: "Organization Admin",
    description: "Administrative access within organization",
    userType: "internal",
    level: 80,
  },

  [USER_ROLES.ORGANIZATION_MEMBER]: {
    name: "Organization Member",
    description: "Standard member access within organization",
    userType: "internal",
    level: 60,
  },

  [USER_ROLES.ORGANIZATION_VIEWER]: {
    name: "Organization Viewer",
    description: "Read-only access within organization",
    userType: "internal",
    level: 40,
  },

  [USER_ROLES.CLIENT_ADMIN]: {
    name: "Client Admin",
    description: "Administrative access to client portal",
    userType: "client",
    level: 70,
  },

  [USER_ROLES.CLIENT_USER]: {
    name: "Client User",
    description: "Standard client portal access",
    userType: "client",
    level: 50,
  },

  [USER_ROLES.CLIENT_VIEWER]: {
    name: "Client Viewer",
    description: "Read-only client portal access",
    userType: "client",
    level: 30,
  },
};

export function getRoleDefinition(role: UserRole): RoleDefinition {
  return ROLE_DEFINITIONS[role];
}

export function isInternalRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "internal";
}

export function isClientRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "client";
}

export function hasHigherRole(
  userRole: UserRole,
  requiredRole: UserRole
): boolean {
  return (
    ROLE_DEFINITIONS[userRole].level >= ROLE_DEFINITIONS[requiredRole].level
  );
}
```

**File: `core/auth/permissions.ts`**

```typescript
import { UserRole, USER_ROLES, ROLE_DEFINITIONS } from "./roles";

export const PERMISSIONS = {
  // Organization permissions
  ORGANIZATION_READ: "organization:read",
  ORGANIZATION_WRITE: "organization:write",
  ORGANIZATION_DELETE: "organization:delete",
  ORGANIZATION_MANAGE_MEMBERS: "organization:manage_members",
  ORGANIZATION_MANAGE_SETTINGS: "organization:manage_settings",

  // User permissions
  USER_READ: "user:read",
  USER_WRITE: "user:write",
  USER_DELETE: "user:delete",
  USER_INVITE: "user:invite",
  USER_MANAGE_ROLES: "user:manage_roles",

  // Client permissions
  CLIENT_READ: "client:read",
  CLIENT_WRITE: "client:write",
  CLIENT_DELETE: "client:delete",
  CLIENT_INVITE: "client:invite",
  CLIENT_PORTAL_ACCESS: "client:portal_access",

  // Project permissions
  PROJECT_READ: "project:read",
  PROJECT_WRITE: "project:write",
  PROJECT_DELETE: "project:delete",
  PROJECT_MANAGE_TASKS: "project:manage_tasks",
  PROJECT_MANAGE_TEAM: "project:manage_team",

  // Task permissions
  TASK_READ: "task:read",
  TASK_WRITE: "task:write",
  TASK_DELETE: "task:delete",
  TASK_ASSIGN: "task:assign",
  TASK_TIME_TRACK: "task:time_track",

  // Invoice permissions
  INVOICE_READ: "invoice:read",
  INVOICE_WRITE: "invoice:write",
  INVOICE_DELETE: "invoice:delete",
  INVOICE_SEND: "invoice:send",
  INVOICE_PAYMENT: "invoice:payment",

  // Financial permissions
  FINANCIAL_READ: "financial:read",
  FINANCIAL_WRITE: "financial:write",
  FINANCIAL_REPORTS: "financial:reports",
} as const;

export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [USER_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),

  [USER_ROLES.ORGANIZATION_OWNER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_WRITE,
    PERMISSIONS.ORGANIZATION_DELETE,
    PERMISSIONS.ORGANIZATION_MANAGE_MEMBERS,
    PERMISSIONS.ORGANIZATION_MANAGE_SETTINGS,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_MANAGE_ROLES,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.CLIENT_WRITE,
    PERMISSIONS.CLIENT_DELETE,
    PERMISSIONS.CLIENT_INVITE,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_DELETE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.PROJECT_MANAGE_TEAM,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_DELETE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.INVOICE_WRITE,
    PERMISSIONS.INVOICE_DELETE,
    PERMISSIONS.INVOICE_SEND,
    PERMISSIONS.INVOICE_PAYMENT,
    PERMISSIONS.FINANCIAL_READ,
    PERMISSIONS.FINANCIAL_WRITE,
    PERMISSIONS.FINANCIAL_REPORTS,
  ],

  [USER_ROLES.ORGANIZATION_ADMIN]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_WRITE,
    PERMISSIONS.ORGANIZATION_MANAGE_MEMBERS,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.CLIENT_WRITE,
    PERMISSIONS.CLIENT_INVITE,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.PROJECT_MANAGE_TEAM,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.INVOICE_WRITE,
    PERMISSIONS.INVOICE_SEND,
    PERMISSIONS.INVOICE_PAYMENT,
    PERMISSIONS.FINANCIAL_READ,
    PERMISSIONS.FINANCIAL_WRITE,
    PERMISSIONS.FINANCIAL_REPORTS,
  ],

  [USER_ROLES.ORGANIZATION_MEMBER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],

  [USER_ROLES.ORGANIZATION_VIEWER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],

  [USER_ROLES.CLIENT_ADMIN]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.USER_READ,
  ],

  [USER_ROLES.CLIENT_USER]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
  ],

  [USER_ROLES.CLIENT_VIEWER]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.INVOICE_READ,
  ],
};

export function hasPermission(
  userRole: UserRole,
  permission: Permission,
  customPermissions?: Permission[]
): boolean {
  // Check custom permissions first
  if (customPermissions?.includes(permission)) {
    return true;
  }

  // Check role-based permissions
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  return rolePermissions?.includes(permission) || false;
}

export function hasAnyPermission(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.some((permission) =>
    hasPermission(userRole, permission, customPermissions)
  );
}

export function hasAllPermissions(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.every((permission) =>
    hasPermission(userRole, permission, customPermissions)
  );
}

export function requirePermission(permission: Permission) {
  return (userRole: UserRole, customPermissions?: Permission[]) => {
    if (!hasPermission(userRole, permission, customPermissions)) {
      throw new Error(`Permission denied: ${permission}`);
    }
  };
}

export function getPermissionsForRole(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}
```

### Response Helpers

**File: `core/api/response.ts`**

```typescript
import { NextResponse } from "next/server";
import { ApiResponse } from "./types";

export function apiSuccess<T>(
  data: T,
  meta?: Partial<ApiResponse<T>["meta"]>
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      ...meta,
    },
  });
}

export function apiError(
  code: string,
  message: string,
  status: number = 400,
  details?: any
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message,
        details,
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    },
    { status }
  );
}

export function apiValidationError(
  errors: Record<string, string[]>
): NextResponse<ApiResponse> {
  return apiError("VALIDATION_ERROR", "Validation failed", 400, {
    fields: errors,
  });
}

export function apiNotFound(
  resource: string = "Resource"
): NextResponse<ApiResponse> {
  return apiError("NOT_FOUND", `${resource} not found`, 404);
}

export function apiUnauthorized(
  message: string = "Authentication required"
): NextResponse<ApiResponse> {
  return apiError("UNAUTHORIZED", message, 401);
}

export function apiForbidden(
  message: string = "Access denied"
): NextResponse<ApiResponse> {
  return apiError("FORBIDDEN", message, 403);
}
```

### API Middleware

**File: `core/api/middleware.ts`**

```typescript
import { NextRequest } from "next/server";
import { auth } from "@/core/auth/server";
import { apiUnauthorized, apiForbidden, apiError } from "./response";
import { ApiRequest } from "./types";
import { UserRole, isInternalRole, isClientRole } from "@/core/auth/roles";
import { Permission, hasPermission } from "@/core/auth/permissions";
import { getConfig } from "@/core/config";

interface AuthOptions {
  requireUserType?: "internal" | "client";
  requireOrganization?: boolean;
  requirePermissions?: Permission[];
  requireAnyPermission?: Permission[];
  requireRole?: UserRole;
  allowPublic?: boolean;
}

export async function withApiAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: AuthOptions = {}
) {
  return async (request: NextRequest) => {
    try {
      const config = getConfig();

      // Allow public access if specified
      if (options.allowPublic) {
        const context: ApiRequest = {
          user: {
            id: "anonymous",
            email: "<EMAIL>",
            userType: "internal",
            organizationId: undefined,
            clientId: undefined,
            roles: [],
            permissions: [],
          },
          organizationId: "",
        };
        return await handler(request, context);
      }

      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session) {
        return apiUnauthorized();
      }

      const { user } = session;

      // Get user roles and permissions from database
      const userRoles = await getUserRoles(user.id, user.organizationId);
      const userPermissions = await getUserPermissions(user.id, userRoles);

      // Check user type requirement
      if (options.requireUserType) {
        const hasCorrectUserType = userRoles.some((role) => {
          if (options.requireUserType === "internal") {
            return isInternalRole(role);
          } else {
            return isClientRole(role);
          }
        });

        if (!hasCorrectUserType) {
          return apiForbidden("Invalid user type for this endpoint");
        }
      }

      // Check organization requirement
      if (options.requireOrganization && !user.organizationId) {
        return apiForbidden("Organization membership required");
      }

      // Check role requirement
      if (options.requireRole) {
        if (!userRoles.includes(options.requireRole)) {
          return apiForbidden(`Role required: ${options.requireRole}`);
        }
      }

      // Check permission requirements
      if (options.requirePermissions?.length) {
        const hasAllPermissions = options.requirePermissions.every(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAllPermissions) {
          return apiForbidden("Insufficient permissions");
        }
      }

      // Check any permission requirement
      if (options.requireAnyPermission?.length) {
        const hasAnyRequiredPermission = options.requireAnyPermission.some(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAnyRequiredPermission) {
          return apiForbidden("Insufficient permissions");
        }
      }

      const context: ApiRequest = {
        user: {
          id: user.id,
          email: user.email,
          userType: user.userType,
          organizationId: user.organizationId,
          clientId: user.clientId,
          roles: userRoles,
          permissions: userPermissions,
        },
        organizationId: user.organizationId || "",
      };

      return await handler(request, context);
    } catch (error) {
      console.error("API Auth Error:", error);
      return apiError("INTERNAL_ERROR", "Authentication failed", 500);
    }
  };
}

// Helper functions to get user roles and permissions
async function getUserRoles(
  userId: string,
  organizationId?: string
): Promise<UserRole[]> {
  // This would query the database for user's roles
  // For now, return a default role based on user type
  // In real implementation, this would query organizationMembers table
  return ["organization_member" as UserRole]; // Placeholder
}

async function getUserPermissions(
  userId: string,
  roles: UserRole[]
): Promise<Permission[]> {
  // This would query the database for user's custom permissions
  // For now, return empty array as permissions come from roles
  return []; // Placeholder
}

// Convenience middleware functions
export function withInternalAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "internal" });
}

export function withClientAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "client" });
}

export function withPermissions(
  permissions: Permission[],
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requirePermissions"> = {}
) {
  return withApiAuth(handler, { ...options, requirePermissions: permissions });
}
```

### Validation Helpers

**File: `core/api/validation.ts`**

```typescript
import { z } from "zod";
import { apiValidationError } from "./response";

export function validateRequest<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; response: Response } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string[]> = {};

      error.errors.forEach((err) => {
        const path = err.path.join(".");
        if (!fieldErrors[path]) {
          fieldErrors[path] = [];
        }
        fieldErrors[path].push(err.message);
      });

      return {
        success: false,
        response: apiValidationError(fieldErrors),
      };
    }

    return {
      success: false,
      response: apiError("VALIDATION_ERROR", "Invalid request data"),
    };
  }
}

export async function validateJsonBody<T>(
  request: Request,
  schema: z.ZodSchema<T>
): Promise<
  { success: true; data: T } | { success: false; response: Response }
> {
  try {
    const body = await request.json();
    return validateRequest(schema, body);
  } catch (error) {
    return {
      success: false,
      response: apiError("INVALID_JSON", "Invalid JSON in request body"),
    };
  }
}
```

### Pagination Helper

**File: `core/api/pagination.ts`**

```typescript
import { PaginationParams } from "./types";

export function parsePaginationParams(
  searchParams: URLSearchParams
): PaginationParams {
  return {
    page: Math.max(1, parseInt(searchParams.get("page") || "1")),
    limit: Math.min(
      100,
      Math.max(1, parseInt(searchParams.get("limit") || "20"))
    ),
    sortBy: searchParams.get("sortBy") || "createdAt",
    sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
  };
}

export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
) {
  return {
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}
```

### Health Check Endpoint

**File: `app/api/v1/health/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { apiSuccess, apiError } from "@/core/api/response";
import { getDb } from "@/core/database";

export async function GET(request: NextRequest) {
  try {
    // Check database connection
    const db = getDb();
    await db.execute("SELECT 1");

    return apiSuccess({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      services: {
        database: "connected",
        auth: "operational",
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return apiError("HEALTH_CHECK_FAILED", "Service health check failed", 503, {
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
```

### Error Logging

**File: `core/api/logging.ts`**

```typescript
import { getDb } from "@/core/database";
import { activityLogs } from "@/core/database/schema";

export async function logApiActivity(
  organizationId: string,
  userId: string,
  action: string,
  entityType: string,
  entityId: string,
  details?: any
) {
  try {
    const db = getDb();
    await db.insert(activityLogs).values({
      organizationId,
      userId,
      entityType,
      entityId,
      action,
      newValues: JSON.stringify(details),
      ipAddress: "", // Will be populated from request
      userAgent: "", // Will be populated from request
    });
  } catch (error) {
    console.error("Failed to log API activity:", error);
  }
}
```

## Definition of Done

- [ ] API route structure is established under `/api/v1/`
- [ ] Response helpers provide consistent JSON responses
- [ ] Authentication middleware validates user sessions
- [ ] Error handling provides consistent error responses
- [ ] Validation helpers work with Zod schemas
- [ ] Pagination helpers support list endpoints
- [ ] Health check endpoint is functional
- [ ] Activity logging captures API usage
- [ ] User context is available in all protected endpoints
- [ ] Organization-based data isolation is enforced
- [ ] HTTP status codes are used correctly
- [ ] API versioning structure supports future versions
- [ ] Error responses include proper error codes and messages
- [ ] Request validation prevents invalid data processing
- [ ] Database errors are handled gracefully
- [ ] Authentication middleware validates user sessions
- [ ] Error handling provides consistent error responses
- [ ] Validation helpers work with Zod schemas
- [ ] Pagination helpers support list endpoints
- [ ] Health check endpoint is functional
- [ ] Activity logging captures API usage
- [ ] User context is available in all protected endpoints
- [ ] Organization-based data isolation is enforced
- [ ] HTTP status codes are used correctly
- [ ] API versioning structure supports future versions
- [ ] Error responses include proper error codes and messages
- [ ] Request validation prevents invalid data processing
- [ ] Database errors are handled gracefully
