# Task 6: Core API Foundation and Structure

## Task Title

Establish the foundational API structure with authentication middleware, error handling, and response patterns

## Context

This task creates the core API infrastructure that all subsequent endpoints will build upon. We need to establish consistent patterns for request handling, authentication, error responses, and data validation that will be used throughout the platform.

The API foundation must support both internal users (dashboard/admin interfaces) and client users (client portal), with proper data isolation and security measures. This includes setting up middleware, response helpers, and validation patterns that ensure consistency across all endpoints.

## Development Guidelines

**Configuration Management:**

- All configuration must be accessed through `/core/config.ts` - never read environment variables directly
- Use configuration helper functions like `getAuthConfig()`, `getDatabaseConfig()` for specific settings
- Configuration validation must be handled by the centralized configuration system

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in API interfaces
- Use proper TypeScript interfaces and types for all API request/response objects
- Implement proper type guards for request validation and user context

**Route Structure:**

- Internal API routes must be organized under `/~/api/` prefix (dashboard/admin)
- Public API routes must be under `/api/` prefix (webhooks, auth callbacks, health)
- Ensure proper separation between internal and client-facing functionality

**Authentication & Authorization:**

- Use role and permission system from `/core/auth/roles.ts` and `/core/auth/permissions.ts`
- Implement middleware that integrates with Better Auth for session validation
- Support both internal and client user authentication flows

**Documentation Requirements:**

- Before implementing API features, use Context7 MCP tool to retrieve latest Next.js App Router documentation
- Ensure middleware and route handlers follow current Next.js best practices
- Document all API patterns and middleware usage

## Acceptance Criteria

1. **API Structure**

   - Internal API routes organized under `/~/api/` prefix
   - Public API routes organized under `/api/` prefix (webhooks, auth callbacks)
   - Standardized request/response patterns with proper TypeScript interfaces
   - API versioning support for future compatibility

2. **Authentication Integration**

   - All protected endpoints validate user sessions through centralized middleware
   - User context is available in all API handlers with proper typing
   - Organization-based data isolation is enforced
   - Role-based access control using centralized role/permission system

3. **Error Handling**

   - Centralized error handling with consistent error responses
   - Proper logging of API errors and activities
   - Validation errors are properly formatted with TypeScript interfaces
   - Database errors are handled gracefully

4. **Response Patterns**
   - Consistent JSON response structure with TypeScript interfaces
   - Pagination support for list endpoints
   - Metadata included in responses (timestamps, counts, etc.)
   - Success and error responses follow the same pattern

## Dependencies

- Task 1: Database Schema Implementation
- Task 3: Centralized Configuration System
- Task 4: Better Auth Configuration
- Task 5: Role-Based Access Control System
- Next.js App Router setup

## Technical Requirements

### API Route Structure

```
app/
├── ~/
│   └── api/                    # Internal APIs (dashboard/admin)
│       ├── organizations/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── users/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── clients/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       ├── projects/
│       │   ├── route.ts
│       │   └── [id]/route.ts
│       └── invoices/
│           ├── route.ts
│           └── [id]/route.ts
└── api/                        # Public APIs
    ├── auth/                   # Better Auth endpoints
    │   └── [...auth]/route.ts
    ├── webhooks/               # External webhooks
    │   ├── stripe/route.ts
    │   └── resend/route.ts
    └── health/route.ts         # Public health check
```

### Core API Types

**File: `core/api/types.ts`**

```typescript
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  meta: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiRequest {
  user: {
    id: string;
    email: string;
    userType: "internal" | "client";
    organizationId?: string;
    clientId?: string;
    roles: string[];
    permissions: string[];
  };
  organizationId: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}
```

### Response Helpers

**File: `core/api/response.ts`**

```typescript
import { NextResponse } from "next/server";
import { ApiResponse, ValidationError } from "./types";

export function apiSuccess<T>(
  data: T,
  meta?: Partial<ApiResponse<T>["meta"]>
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      ...meta,
    },
  });
}

export function apiError(
  code: string,
  message: string,
  status: number = 400,
  details?: Record<string, unknown>
): NextResponse<ApiResponse<never>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message,
        details,
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    },
    { status }
  );
}

export function apiValidationError(
  errors: ValidationError[]
): NextResponse<ApiResponse<never>> {
  return apiError("VALIDATION_ERROR", "Validation failed", 400, {
    fields: errors,
  });
}

export function apiUnauthorized(
  message: string = "Authentication required"
): NextResponse<ApiResponse<never>> {
  return apiError("UNAUTHORIZED", message, 401);
}

export function apiForbidden(
  message: string = "Access denied"
): NextResponse<ApiResponse<never>> {
  return apiError("FORBIDDEN", message, 403);
}

export function apiNotFound(
  resource: string = "Resource"
): NextResponse<ApiResponse<never>> {
  return apiError("NOT_FOUND", `${resource} not found`, 404);
}

export function apiInternalError(
  message: string = "Internal server error"
): NextResponse<ApiResponse<never>> {
  return apiError("INTERNAL_ERROR", message, 500);
}
```

### Authentication Middleware

**File: `core/api/middleware.ts`**

```typescript
import { NextRequest } from "next/server";
import { auth } from "@/core/auth/server";
import { apiUnauthorized, apiForbidden, apiInternalError } from "./response";
import { ApiRequest } from "./types";
import { UserRole, isInternalRole, isClientRole } from "@/core/auth/roles";
import { Permission, hasPermission } from "@/core/auth/permissions";

interface AuthOptions {
  requireUserType?: "internal" | "client";
  requireOrganization?: boolean;
  requirePermissions?: Permission[];
  requireAnyPermission?: Permission[];
  requireRole?: UserRole;
  allowPublic?: boolean;
}

export async function withApiAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: AuthOptions = {}
) {
  return async (request: NextRequest) => {
    try {
      // Allow public access if specified
      if (options.allowPublic) {
        const context: ApiRequest = {
          user: {
            id: "anonymous",
            email: "<EMAIL>",
            userType: "internal",
            organizationId: undefined,
            clientId: undefined,
            roles: [],
            permissions: [],
          },
          organizationId: "",
        };
        return await handler(request, context);
      }

      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session) {
        return apiUnauthorized();
      }

      const { user } = session;

      // Get user roles and permissions from database
      const userRoles = await getUserRoles(user.id, user.organizationId);
      const userPermissions = await getUserPermissions(user.id, userRoles);

      // Check user type requirement
      if (options.requireUserType) {
        const hasCorrectUserType = userRoles.some((role) => {
          if (options.requireUserType === "internal") {
            return isInternalRole(role);
          } else {
            return isClientRole(role);
          }
        });

        if (!hasCorrectUserType) {
          return apiForbidden("Invalid user type for this endpoint");
        }
      }

      // Check organization requirement
      if (options.requireOrganization && !user.organizationId) {
        return apiForbidden("Organization membership required");
      }

      // Check role requirement
      if (options.requireRole) {
        if (!userRoles.includes(options.requireRole)) {
          return apiForbidden(`Role required: ${options.requireRole}`);
        }
      }

      // Check permission requirements
      if (options.requirePermissions?.length) {
        const hasAllPermissions = options.requirePermissions.every(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAllPermissions) {
          return apiForbidden("Insufficient permissions");
        }
      }

      // Check any permission requirement
      if (options.requireAnyPermission?.length) {
        const hasAnyRequiredPermission = options.requireAnyPermission.some(
          (permission) =>
            userRoles.some((role) =>
              hasPermission(role, permission, userPermissions)
            )
        );

        if (!hasAnyRequiredPermission) {
          return apiForbidden("Insufficient permissions");
        }
      }

      const context: ApiRequest = {
        user: {
          id: user.id,
          email: user.email,
          userType: user.userType,
          organizationId: user.organizationId,
          clientId: user.clientId,
          roles: userRoles,
          permissions: userPermissions,
        },
        organizationId: user.organizationId || "",
      };

      return await handler(request, context);
    } catch (error) {
      console.error("API Auth Error:", error);
      return apiInternalError("Authentication failed");
    }
  };
}

// Helper functions to get user roles and permissions
async function getUserRoles(
  userId: string,
  organizationId?: string
): Promise<UserRole[]> {
  // This would query the database for user's roles
  // Implementation will be completed when database queries are available
  return ["organization_member" as UserRole]; // Placeholder
}

async function getUserPermissions(
  userId: string,
  roles: UserRole[]
): Promise<Permission[]> {
  // This would query the database for user's custom permissions
  // Implementation will be completed when database queries are available
  return []; // Placeholder
}

// Convenience middleware functions
export function withInternalAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "internal" });
}

export function withClientAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requireUserType"> = {}
) {
  return withApiAuth(handler, { ...options, requireUserType: "client" });
}

export function withPermissions(
  permissions: Permission[],
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options: Omit<AuthOptions, "requirePermissions"> = {}
) {
  return withApiAuth(handler, { ...options, requirePermissions: permissions });
}
```

### Validation Helpers

**File: `core/api/validation.ts`**

```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { apiValidationError } from "./response";
import { ValidationError, PaginationParams } from "./types";

export async function validateJsonBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<
  { success: true; data: T } | { success: false; response: Response }
> {
  try {
    const body = await request.json();
    const result = schema.safeParse(body);

    if (!result.success) {
      const errors: ValidationError[] = result.error.errors.map((err) => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      }));

      return {
        success: false,
        response: apiValidationError(errors),
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    return {
      success: false,
      response: apiValidationError([
        {
          field: "body",
          message: "Invalid JSON format",
          code: "invalid_json",
        },
      ]),
    };
  }
}

export function parsePaginationParams(
  searchParams: URLSearchParams
): PaginationParams {
  return {
    page: Math.max(1, parseInt(searchParams.get("page") || "1")),
    limit: Math.min(
      100,
      Math.max(1, parseInt(searchParams.get("limit") || "20"))
    ),
    sortBy: searchParams.get("sortBy") || "createdAt",
    sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
  };
}

export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
) {
  return {
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}
```

### Health Check Endpoint

**File: `app/api/health/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { apiSuccess, apiInternalError } from "@/core/api/response";
import { getDb } from "@/core/database";
import { getConfig } from "@/core/config";

export async function GET(request: NextRequest) {
  try {
    const config = getConfig();

    // Check database connection
    const db = getDb();
    const dbStart = Date.now();
    await db.execute("SELECT 1");
    const dbTime = Date.now() - dbStart;

    const services = {
      database: dbTime < 1000 ? "connected" : "slow",
      auth: "operational",
      api: "operational",
    };

    const overallStatus = Object.values(services).every((status) =>
      ["connected", "operational"].includes(status)
    )
      ? "healthy"
      : "degraded";

    return apiSuccess({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.environment,
      services,
      metrics: {
        databaseResponseTime: dbTime,
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return apiInternalError("Service health check failed");
  }
}
```

## Definition of Done

- [ ] API route structure follows `/~/api` for internal and `/api` for public APIs
- [ ] TypeScript interfaces are defined for all API request/response objects
- [ ] Response helpers provide consistent JSON responses
- [ ] Authentication middleware validates user sessions
- [ ] Error handling provides consistent error responses
- [ ] Validation helpers work with Zod schemas
- [ ] Pagination helpers support list endpoints
- [ ] Health check endpoint is functional
- [ ] Activity logging captures API usage
- [ ] User context is available in all protected endpoints
- [ ] Organization-based data isolation is enforced
- [ ] HTTP status codes are used correctly
- [ ] API versioning structure supports future versions
- [ ] Error responses include proper error codes and messages
- [ ] Request validation prevents invalid data processing
- [ ] Database errors are handled gracefully
