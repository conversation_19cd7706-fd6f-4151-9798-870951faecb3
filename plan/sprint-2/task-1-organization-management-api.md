# Task 1: Organization Management API

## Task Title

Implement comprehensive organization management API with multi-tenant support and role-based access control

## Context

Organizations are the foundation of the multi-tenant architecture. This API enables users to create, manage, and configure organizations while establishing the data isolation boundaries that protect client data. The organization management system must support different user roles, team structures, and proper access controls.

This task creates the core multi-tenancy functionality that all other features will build upon, ensuring proper data isolation and security across different organizations using the platform.

**Modular Architecture Requirements:**

- Organization module is self-contained in `/modules/organization/` directory
- Clear separation between API routes, business logic, and data access
- Uses simplified permission system: `organization.read` and `organization.manage`
- Proper integration with centralized configuration and auth systems

## Development Guidelines

**Configuration Management:**

- All configuration must be accessed through `/core/config.ts` - never read environment variables directly
- Use `getConfig()` and specific helper functions for database and auth settings
- Configuration validation must be handled by the centralized configuration system

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in all organization code
- Use proper TypeScript interfaces and types for all API request/response objects
- Implement proper type guards for request validation and data processing

**Route Structure:**

- Organization API routes must be under `/~/api/organizations/` prefix (internal API)
- Ensure proper separation between internal admin functionality and client-facing features
- Follow RESTful API patterns with consistent endpoint naming

**Authentication & Authorization:**

- Use simplified permission system: `organization.read` and `organization.manage`
- Implement middleware that integrates with role/permission system from Task 5
- Support both internal user management and client organization access

**Documentation Requirements:**

- Before implementing organization features, use Context7 MCP tool to retrieve latest Next.js and database documentation
- Ensure API implementation follows current best practices
- Document all organization management patterns and access control

**Modular Architecture:**

- Organization module must be self-contained with clear interfaces
- Separate business logic from API routes and database operations
- Use dependency injection for testability and maintainability
- Ensure module can be developed and tested independently

## Acceptance Criteria

1. **Organization CRUD Operations**

   - Create new organizations with proper initialization
   - Read organization details with role-based filtering
   - Update organization settings and configuration
   - Soft delete organizations with data retention policies

2. **Multi-tenant Data Isolation**

   - All organization data is properly isolated
   - Users can only access data from their organizations
   - Cross-organization data leaks are prevented
   - Organization switching is supported for multi-org users

3. **Role-Based Access Control**

   - Organization owners have full administrative access
   - Admins can manage organization settings and members
   - Members have limited access based on their role
   - Proper permission validation on all endpoints

4. **Organization Features**
   - Organization settings and preferences management
   - Team structure within organizations
   - Member invitation and management system
   - Activity logging for organization changes

## Dependencies

- Sprint 1: All tasks completed
  - Task 1: Database Schema Implementation
  - Task 3: Centralized Configuration System
  - Task 4: Better Auth Configuration
  - Task 5: Role-Based Access Control System
  - Task 6: Core API Foundation
- Organization schema tables implemented
- User authentication system functional

## Technical Requirements

### Module Structure

```
modules/
└── organization/
    ├── api/
    │   ├── routes.ts             # API route handlers
    │   └── validation.ts         # Request/response validation
    ├── services/
    │   ├── organization.service.ts    # Business logic
    │   └── member.service.ts          # Member management logic
    ├── repositories/
    │   └── organization.repository.ts # Data access layer
    ├── types/
    │   └── organization.types.ts      # TypeScript interfaces
    └── index.ts                       # Module exports
```

### API Endpoints

**Organization Management**

- `GET /~/api/organizations` - List user's organizations (requires `organization.read`)
- `POST /~/api/organizations` - Create new organization (requires `organization.manage`)
- `GET /~/api/organizations/{id}` - Get organization details (requires `organization.read`)
- `PUT /~/api/organizations/{id}` - Update organization (requires `organization.manage`)
- `DELETE /~/api/organizations/{id}` - Delete organization (requires `organization.manage`)

**Organization Members**

- `GET /~/api/organizations/{id}/members` - List organization members (requires `organization.read`)
- `POST /~/api/organizations/{id}/members/invite` - Invite new member (requires `organization.manage`)
- `PUT /~/api/organizations/{id}/members/{userId}` - Update member role (requires `organization.manage`)
- `DELETE /~/api/organizations/{id}/members/{userId}` - Remove member (requires `organization.manage`)

**Organization Settings**

- `GET /~/api/organizations/{id}/settings` - Get organization settings (requires `organization.read`)
- `PUT /~/api/organizations/{id}/settings` - Update organization settings (requires `organization.manage`)

### Data Models

**Organization Creation Request**

```typescript
interface CreateOrganizationRequest {
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: "1-10" | "11-50" | "51-200" | "201-500" | "500+";
  timezone?: string;
  currency?: string;
}
```

**Organization Response**

```typescript
interface OrganizationResponse {
  id: string;
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  timezone: string;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  memberCount: number;
  userRole: "owner" | "admin" | "member";
}
```

### Implementation Files

**File: `app/api/v1/organizations/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { organizations, organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

const createOrganizationSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  website: z.string().url().optional(),
  industry: z.string().max(100).optional(),
  size: z.enum(["1-10", "11-50", "51-200", "201-500", "500+"]).optional(),
  timezone: z.string().default("UTC"),
  currency: z.string().length(3).default("USD"),
});

// GET /api/v1/organizations - List user's organizations
export const GET = withApiAuth(async (request, { user }) => {
  const db = getDb();

  const userOrganizations = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      description: organizations.description,
      website: organizations.website,
      industry: organizations.industry,
      size: organizations.size,
      timezone: organizations.timezone,
      currency: organizations.currency,
      isActive: organizations.isActive,
      createdAt: organizations.createdAt,
      updatedAt: organizations.updatedAt,
      userRole: organizationMembers.role,
    })
    .from(organizations)
    .innerJoin(
      organizationMembers,
      eq(organizations.id, organizationMembers.organizationId)
    )
    .where(eq(organizationMembers.userId, user.id));

  return apiSuccess(userOrganizations);
});

// POST /api/v1/organizations - Create new organization
export const POST = withApiAuth(
  async (request, { user }) => {
    const validation = await validateJsonBody(
      request,
      createOrganizationSchema
    );
    if (!validation.success) {
      return validation.response;
    }

    const db = getDb();
    const organizationId = crypto.randomUUID();

    try {
      // Create organization
      await db.insert(organizations).values({
        id: organizationId,
        ownerId: user.id,
        ...validation.data,
      });

      // Add creator as owner
      await db.insert(organizationMembers).values({
        organizationId,
        userId: user.id,
        role: "owner",
        joinedAt: new Date(),
      });

      // Fetch created organization with user role
      const [createdOrg] = await db
        .select({
          id: organizations.id,
          name: organizations.name,
          description: organizations.description,
          website: organizations.website,
          industry: organizations.industry,
          size: organizations.size,
          timezone: organizations.timezone,
          currency: organizations.currency,
          isActive: organizations.isActive,
          createdAt: organizations.createdAt,
          updatedAt: organizations.updatedAt,
          userRole: organizationMembers.role,
        })
        .from(organizations)
        .innerJoin(
          organizationMembers,
          eq(organizations.id, organizationMembers.organizationId)
        )
        .where(
          and(
            eq(organizations.id, organizationId),
            eq(organizationMembers.userId, user.id)
          )
        );

      return apiSuccess(createdOrg, { status: 201 });
    } catch (error) {
      console.error("Failed to create organization:", error);
      return apiError("CREATION_FAILED", "Failed to create organization", 500);
    }
  },
  { requireUserType: "internal" }
);
```

**File: `app/api/v1/organizations/[id]/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { organizations, organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

const updateOrganizationSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  website: z.string().url().optional(),
  industry: z.string().max(100).optional(),
  size: z.enum(["1-10", "11-50", "51-200", "201-500", "500+"]).optional(),
  timezone: z.string().optional(),
  currency: z.string().length(3).optional(),
});

// GET /api/v1/organizations/{id} - Get organization details
export const GET = withApiAuth(async (request, { user }, { params }) => {
  const organizationId = params.id;
  const db = getDb();

  // Verify user has access to this organization
  const [organization] = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      description: organizations.description,
      website: organizations.website,
      industry: organizations.industry,
      size: organizations.size,
      timezone: organizations.timezone,
      currency: organizations.currency,
      isActive: organizations.isActive,
      createdAt: organizations.createdAt,
      updatedAt: organizations.updatedAt,
      userRole: organizationMembers.role,
    })
    .from(organizations)
    .innerJoin(
      organizationMembers,
      eq(organizations.id, organizationMembers.organizationId)
    )
    .where(
      and(
        eq(organizations.id, organizationId),
        eq(organizationMembers.userId, user.id)
      )
    );

  if (!organization) {
    return apiNotFound("Organization");
  }

  return apiSuccess(organization);
});

// PUT /api/v1/organizations/{id} - Update organization
export const PUT = withApiAuth(async (request, { user }, { params }) => {
  const organizationId = params.id;
  const validation = await validateJsonBody(request, updateOrganizationSchema);

  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();

  // Verify user has admin access
  const [membership] = await db
    .select({ role: organizationMembers.role })
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.organizationId, organizationId),
        eq(organizationMembers.userId, user.id)
      )
    );

  if (!membership || !["owner", "admin"].includes(membership.role)) {
    return apiError("INSUFFICIENT_PERMISSIONS", "Admin access required", 403);
  }

  try {
    await db
      .update(organizations)
      .set({
        ...validation.data,
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId));

    // Fetch updated organization
    const [updatedOrg] = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        description: organizations.description,
        website: organizations.website,
        industry: organizations.industry,
        size: organizations.size,
        timezone: organizations.timezone,
        currency: organizations.currency,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt,
        userRole: organizationMembers.role,
      })
      .from(organizations)
      .innerJoin(
        organizationMembers,
        eq(organizations.id, organizationMembers.organizationId)
      )
      .where(
        and(
          eq(organizations.id, organizationId),
          eq(organizationMembers.userId, user.id)
        )
      );

    return apiSuccess(updatedOrg);
  } catch (error) {
    console.error("Failed to update organization:", error);
    return apiError("UPDATE_FAILED", "Failed to update organization", 500);
  }
});
```

### Permission Validation Helper

**File: `core/api/permissions.ts`**

```typescript
import { getDb } from "@/core/database";
import { organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

export async function validateOrganizationAccess(
  userId: string,
  organizationId: string,
  requiredRoles?: string[]
): Promise<{ hasAccess: boolean; role?: string }> {
  const db = getDb();

  const [membership] = await db
    .select({ role: organizationMembers.role })
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.userId, userId),
        eq(organizationMembers.organizationId, organizationId)
      )
    );

  if (!membership) {
    return { hasAccess: false };
  }

  if (requiredRoles && !requiredRoles.includes(membership.role)) {
    return { hasAccess: false, role: membership.role };
  }

  return { hasAccess: true, role: membership.role };
}

export function requireOrganizationRole(roles: string[]) {
  return async (userId: string, organizationId: string) => {
    const { hasAccess, role } = await validateOrganizationAccess(
      userId,
      organizationId,
      roles
    );

    if (!hasAccess) {
      throw new Error(
        role
          ? `Insufficient permissions. Required: ${roles.join(
              ", "
            )}, Current: ${role}`
          : "Organization access denied"
      );
    }

    return role!;
  };
}
```

## Definition of Done

- [ ] Organization CRUD API endpoints are implemented and functional
- [ ] Multi-tenant data isolation prevents cross-organization access
- [ ] Role-based access control validates permissions on all endpoints
- [ ] Organization creation initializes proper member relationships
- [ ] Organization updates require appropriate permissions
- [ ] API responses include user's role within each organization
- [ ] Error handling provides clear feedback for permission issues
- [ ] Organization deletion is implemented with proper safeguards
- [ ] Member management endpoints support invitation workflows
- [ ] Organization settings can be configured per organization
- [ ] Activity logging captures all organization changes
- [ ] API endpoints are properly documented with request/response schemas
- [ ] Validation ensures data integrity for all organization operations
- [ ] Performance is optimized for multi-organization queries
- [ ] Security measures prevent unauthorized organization access
