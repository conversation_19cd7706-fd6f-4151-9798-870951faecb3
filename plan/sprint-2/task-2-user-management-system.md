# Task 2: User Management System

## Task Title

Implement comprehensive user management with role-based permissions and multi-organization support

## Context

The user management system handles both internal users (freelancers, team members) and client users (client portal access) through a unified interface. This system must support complex scenarios like users belonging to multiple organizations, role-based access control, and proper data isolation while maintaining security and usability.

This task builds upon the organization management foundation to create a complete user lifecycle management system that supports the platform's multi-tenant architecture.

**Modular Architecture Requirements:**

- User module is self-contained in `/modules/user/` directory
- Clear separation between user business logic and authentication concerns
- Uses simplified permission system: `user.read` and `user.manage`
- Proper integration with organization and role management systems

## Development Guidelines

**Configuration Management:**

- All configuration must be accessed through `/core/config.ts` - never read environment variables directly
- Use centralized configuration for email, database, and authentication settings
- Configuration validation must be handled by the centralized configuration system

**TypeScript Standards:**

- Strictly prohibit the use of `any` type or `as any` type assertions in all user management code
- Use proper TypeScript interfaces and types for all user-related operations
- Implement proper type guards for user data validation and processing

**Route Structure:**

- User API routes must be under `/~/api/users/` prefix (internal API)
- Ensure proper separation between internal user management and client user access
- Follow RESTful API patterns with consistent endpoint naming

**Authentication & Authorization:**

- Use simplified permission system: `user.read` and `user.manage`
- Integrate with role/permission system for proper access control
- Support both internal and client user management workflows

**Documentation Requirements:**

- Before implementing user features, use Context7 MCP tool to retrieve latest authentication and user management best practices
- Ensure implementation follows current security standards
- Document all user management patterns and access control mechanisms

**Modular Architecture:**

- User module must be self-contained with clear interfaces
- Separate user business logic from authentication and authorization concerns
- Use dependency injection for testability and maintainability
- Ensure module integrates properly with organization and role systems

## Acceptance Criteria

1. **User Profile Management**

   - Users can view and update their profiles
   - Profile information is properly validated and secured
   - User preferences and settings are configurable
   - Avatar/profile image support is implemented

2. **Multi-Organization Support**

   - Users can belong to multiple organizations with different roles
   - Organization switching is seamless and secure
   - Role inheritance and permission management across organizations
   - Primary organization designation for user defaults

3. **Role-Based Access Control**

   - Granular permissions based on user roles within organizations
   - Permission validation on all user operations
   - Role hierarchy enforcement (owner > admin > member)
   - Custom permission sets for specific use cases

4. **User Lifecycle Management**
   - User invitation and onboarding workflows
   - Account activation and email verification
   - User deactivation and data retention policies
   - Audit trails for all user-related activities

## Dependencies

- Sprint 1: All tasks completed
- Sprint 2 Task 1: Organization Management API
- Better Auth configuration with unified user system

## Technical Requirements

### API Endpoints

**User Profile Management**

- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update current user profile
- `GET /api/v1/users/me/organizations` - Get user's organizations
- `POST /api/v1/users/me/organizations/{id}/switch` - Switch active organization

**User Management (Admin)**

- `GET /api/v1/users` - List organization users (admin only)
- `GET /api/v1/users/{id}` - Get user details (admin only)
- `PUT /api/v1/users/{id}` - Update user (admin only)
- `DELETE /api/v1/users/{id}` - Deactivate user (admin only)

**User Invitations**

- `POST /api/v1/users/invite` - Invite new user to organization
- `GET /api/v1/users/invitations` - List pending invitations
- `POST /api/v1/users/invitations/{id}/accept` - Accept invitation
- `DELETE /api/v1/users/invitations/{id}` - Decline invitation

### Data Models

**User Profile Request**

```typescript
interface UpdateUserProfileRequest {
  name?: string;
  email?: string;
  phone?: string;
  timezone?: string;
  language?: string;
  avatar?: string;
  preferences?: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyReports: boolean;
    projectUpdates: boolean;
  };
}
```

**User Profile Response**

```typescript
interface UserProfileResponse {
  id: string;
  name: string;
  email: string;
  phone?: string;
  userType: "internal" | "client";
  avatar?: string;
  timezone: string;
  language: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  preferences: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    weeklyReports: boolean;
    projectUpdates: boolean;
  };
  organizations: Array<{
    id: string;
    name: string;
    role: string;
    isPrimary: boolean;
    joinedAt: string;
  }>;
}
```

### Implementation Files

**File: `app/api/v1/users/me/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import {
  users,
  organizationMembers,
  organizations,
} from "@/core/database/schema";
import { eq } from "drizzle-orm";

const updateProfileSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  phone: z.string().max(20).optional(),
  timezone: z.string().optional(),
  language: z.string().max(10).optional(),
  avatar: z.string().url().optional(),
  preferences: z
    .object({
      emailNotifications: z.boolean().optional(),
      pushNotifications: z.boolean().optional(),
      weeklyReports: z.boolean().optional(),
      projectUpdates: z.boolean().optional(),
    })
    .optional(),
});

// GET /api/v1/users/me - Get current user profile
export const GET = withApiAuth(async (request, { user }) => {
  const db = getDb();

  // Get user details
  const [userDetails] = await db
    .select()
    .from(users)
    .where(eq(users.id, user.id));

  if (!userDetails) {
    return apiError("USER_NOT_FOUND", "User not found", 404);
  }

  // Get user's organizations
  const userOrganizations = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      role: organizationMembers.role,
      isPrimary: organizationMembers.isPrimary,
      joinedAt: organizationMembers.joinedAt,
    })
    .from(organizationMembers)
    .innerJoin(
      organizations,
      eq(organizationMembers.organizationId, organizations.id)
    )
    .where(eq(organizationMembers.userId, user.id));

  const response: UserProfileResponse = {
    id: userDetails.id,
    name: userDetails.name,
    email: userDetails.email,
    phone: userDetails.phone,
    userType: userDetails.userType,
    avatar: userDetails.avatar,
    timezone: userDetails.timezone || "UTC",
    language: userDetails.language || "en",
    isActive: userDetails.isActive,
    emailVerified: userDetails.emailVerified,
    createdAt: userDetails.createdAt.toISOString(),
    updatedAt: userDetails.updatedAt.toISOString(),
    preferences: userDetails.preferences
      ? JSON.parse(userDetails.preferences)
      : {
          emailNotifications: true,
          pushNotifications: true,
          weeklyReports: false,
          projectUpdates: true,
        },
    organizations: userOrganizations.map((org) => ({
      id: org.id,
      name: org.name,
      role: org.role,
      isPrimary: org.isPrimary || false,
      joinedAt: org.joinedAt.toISOString(),
    })),
  };

  return apiSuccess(response);
});

// PUT /api/v1/users/me - Update current user profile
export const PUT = withApiAuth(async (request, { user }) => {
  const validation = await validateJsonBody(request, updateProfileSchema);
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();

  try {
    const updateData: any = {
      ...validation.data,
      updatedAt: new Date(),
    };

    // Handle preferences separately as JSON
    if (validation.data.preferences) {
      updateData.preferences = JSON.stringify(validation.data.preferences);
    }

    await db.update(users).set(updateData).where(eq(users.id, user.id));

    // Return updated user profile
    return GET(request);
  } catch (error) {
    console.error("Failed to update user profile:", error);
    return apiError("UPDATE_FAILED", "Failed to update profile", 500);
  }
});
```

**File: `app/api/v1/users/me/organizations/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess } from "@/core/api/response";
import { getDb } from "@/core/database";
import { organizationMembers, organizations } from "@/core/database/schema";
import { eq } from "drizzle-orm";

// GET /api/v1/users/me/organizations - Get user's organizations
export const GET = withApiAuth(async (request, { user }) => {
  const db = getDb();

  const userOrganizations = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      description: organizations.description,
      website: organizations.website,
      industry: organizations.industry,
      timezone: organizations.timezone,
      currency: organizations.currency,
      isActive: organizations.isActive,
      role: organizationMembers.role,
      isPrimary: organizationMembers.isPrimary,
      joinedAt: organizationMembers.joinedAt,
      permissions: organizationMembers.permissions,
    })
    .from(organizationMembers)
    .innerJoin(
      organizations,
      eq(organizationMembers.organizationId, organizations.id)
    )
    .where(eq(organizationMembers.userId, user.id))
    .orderBy(organizationMembers.isPrimary, organizationMembers.joinedAt);

  return apiSuccess(userOrganizations);
});
```

**File: `app/api/v1/users/me/organizations/[id]/switch/route.ts`**

```typescript
import { NextRequest } from "next/server";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { getDb } from "@/core/database";
import { organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

// POST /api/v1/users/me/organizations/{id}/switch - Switch active organization
export const POST = withApiAuth(async (request, { user }, { params }) => {
  const organizationId = params.id;
  const db = getDb();

  // Verify user has access to this organization
  const [membership] = await db
    .select()
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.userId, user.id),
        eq(organizationMembers.organizationId, organizationId)
      )
    );

  if (!membership) {
    return apiNotFound("Organization membership");
  }

  try {
    // Update user's primary organization
    await db.transaction(async (tx) => {
      // Remove primary flag from all user's organizations
      await tx
        .update(organizationMembers)
        .set({ isPrimary: false })
        .where(eq(organizationMembers.userId, user.id));

      // Set new primary organization
      await tx
        .update(organizationMembers)
        .set({ isPrimary: true })
        .where(
          and(
            eq(organizationMembers.userId, user.id),
            eq(organizationMembers.organizationId, organizationId)
          )
        );
    });

    return apiSuccess({
      message: "Organization switched successfully",
      organizationId,
    });
  } catch (error) {
    console.error("Failed to switch organization:", error);
    return apiError("SWITCH_FAILED", "Failed to switch organization", 500);
  }
});
```

### Permission System

**File: `core/auth/permissions.ts`**

```typescript
export const PERMISSIONS = {
  // Organization permissions
  ORGANIZATION_READ: "organization:read",
  ORGANIZATION_WRITE: "organization:write",
  ORGANIZATION_DELETE: "organization:delete",
  ORGANIZATION_MANAGE_MEMBERS: "organization:manage_members",

  // Project permissions
  PROJECT_READ: "project:read",
  PROJECT_WRITE: "project:write",
  PROJECT_DELETE: "project:delete",
  PROJECT_MANAGE_TASKS: "project:manage_tasks",

  // Client permissions
  CLIENT_READ: "client:read",
  CLIENT_WRITE: "client:write",
  CLIENT_DELETE: "client:delete",

  // Financial permissions
  INVOICE_READ: "invoice:read",
  INVOICE_WRITE: "invoice:write",
  INVOICE_DELETE: "invoice:delete",
  PAYMENT_READ: "payment:read",
  PAYMENT_WRITE: "payment:write",

  // User permissions
  USER_READ: "user:read",
  USER_WRITE: "user:write",
  USER_DELETE: "user:delete",
} as const;

export const ROLE_PERMISSIONS = {
  owner: Object.values(PERMISSIONS),
  admin: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_WRITE,
    PERMISSIONS.ORGANIZATION_MANAGE_MEMBERS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_DELETE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.CLIENT_WRITE,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.INVOICE_WRITE,
    PERMISSIONS.PAYMENT_READ,
    PERMISSIONS.PAYMENT_WRITE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
  ],
  member: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.PAYMENT_READ,
    PERMISSIONS.USER_READ,
  ],
  viewer: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.PAYMENT_READ,
    PERMISSIONS.USER_READ,
  ],
} as const;

export function hasPermission(
  userRole: string,
  permission: string,
  customPermissions?: string[]
): boolean {
  // Check custom permissions first
  if (customPermissions?.includes(permission)) {
    return true;
  }

  // Check role-based permissions
  const rolePermissions =
    ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS];
  return rolePermissions?.includes(permission as any) || false;
}

export function requirePermission(permission: string) {
  return (userRole: string, customPermissions?: string[]) => {
    if (!hasPermission(userRole, permission, customPermissions)) {
      throw new Error(`Permission denied: ${permission}`);
    }
  };
}
```

## Definition of Done

- [ ] User profile management API is fully functional
- [ ] Multi-organization support allows users to belong to multiple organizations
- [ ] Organization switching updates user's active context securely
- [ ] Role-based permissions are enforced on all user operations
- [ ] User preferences and settings are properly managed
- [ ] User invitation workflow is implemented and tested
- [ ] Account activation and email verification work correctly
- [ ] User deactivation preserves data integrity
- [ ] Audit trails capture all user-related activities
- [ ] Permission system supports granular access control
- [ ] API responses include proper user context and organization information
- [ ] Error handling provides clear feedback for permission issues
- [ ] User data validation ensures data integrity
- [ ] Security measures prevent unauthorized user access
- [ ] Performance is optimized for multi-organization user queries
