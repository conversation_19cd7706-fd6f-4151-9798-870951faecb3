# Freelancer Management Platform - Database Schema Plan

## Current Schema Analysis

### ✅ Already Implemented

The current schema (`core/database/schema.ts`) includes:

1. **Authentication System** (Better Auth integration)

   - `users` - User accounts with 2FA support
   - `sessions` - Session management
   - `accounts` - OAuth and password providers
   - `verifications` - Email/phone verification
   - `twoFactors` - 2FA authentication

2. **Organization & Team Management**

   - `organizations` - Multi-tenant organization structure
   - `organizationMembers` - Organization membership
   - `organizationInvitations` - Organization invites
   - `organizationMemberPermissions` - RBAC permissions
   - `teams` - Team structure within organizations
   - `teamMembers` - Team membership
   - `teamInvitations` - Team invites
   - `teamMemberPermissions` - Team-level permissions

3. **Basic Client Management**

   - `clients` - Client information
   - `clientBillingAddress` - Client billing addresses

4. **Basic Project & Task Management**

   - `projects` - Project structure
   - `projectTasks` - Hierarchical task structure
   - `projectTaskAssignees` - Task assignments
   - `projectTaskComments` - Task commenting system
   - `projectTaskAttachments` - File attachments

5. **Basic Financial Management**
   - `invoices` - Invoice tracking
   - `invoiceItems` - Invoice line items

### ❌ Missing Components

## Required Schema Additions

### 1. Enhanced Client Management

#### Client Contact Information

```sql
-- Multiple contact persons per client
client_contacts (
  id, client_id, name, email, phone, position, is_primary,
  created_at, updated_at, deleted_at
)

-- Client communication preferences
client_preferences (
  id, client_id, communication_method, timezone, language,
  notification_settings, created_at, updated_at
)
```

#### Client Portal Access

```sql
-- Client portal users (separate from main users)
client_users (
  id, client_id, email, password_hash, name, role,
  last_login, is_active, created_at, updated_at, deleted_at
)

-- Client portal sessions
client_sessions (
  id, client_user_id, token, expires_at, ip_address,
  user_agent, created_at, updated_at
)
```

### 2. Enhanced Project & Task Management

#### Sprint Management

```sql
-- Project sprints
project_sprints (
  id, project_id, name, description, start_date, end_date,
  status, created_by, created_at, updated_at, deleted_at
)

-- Link tasks to sprints
-- Add sprint_id to existing projectTasks table
```

#### Task Status & Dependencies

```sql
-- Custom task statuses per organization
task_statuses (
  id, organization_id, name, color, order, is_default,
  created_at, updated_at, deleted_at
)

-- Task dependencies
task_dependencies (
  id, task_id, depends_on_task_id, dependency_type,
  created_at, updated_at
)

-- Add status_id to existing projectTasks table
```

#### Project Comments

```sql
-- Project-level comments
project_comments (
  id, project_id, user_id, parent_id, comment,
  created_at, updated_at, deleted_at
)
```

### 3. Financial Management Enhancement

#### Receipts & Expenses

```sql
-- Business receipts
receipts (
  id, organization_id, vendor_name, amount, currency,
  receipt_date, category, description, file_url,
  created_by, created_at, updated_at, deleted_at
)

-- Expense categories
expense_categories (
  id, organization_id, name, description, is_active,
  created_at, updated_at, deleted_at
)
```

#### Payment Management

```sql
-- Invoice payments
invoice_payments (
  id, invoice_id, amount, payment_date, payment_method,
  transaction_id, notes, created_at, updated_at
)

-- Payment reminders
payment_reminders (
  id, invoice_id, reminder_type, sent_at, status,
  created_at, updated_at
)

-- Late fees
late_fees (
  id, invoice_id, fee_amount, applied_date, reason,
  created_at, updated_at
)
```

### 4. Contract Management

```sql
-- Contract templates
contract_templates (
  id, organization_id, name, content, variables,
  is_active, created_by, created_at, updated_at, deleted_at
)

-- Client contracts
contracts (
  id, client_id, project_id, template_id, title,
  content, status, signed_date, expires_date,
  created_by, created_at, updated_at, deleted_at
)

-- Contract signatures
contract_signatures (
  id, contract_id, signer_name, signer_email,
  signature_data, signed_at, ip_address,
  created_at, updated_at
)
```

### 5. Client Portal Features

#### Support Tickets

```sql
-- Support tickets
support_tickets (
  id, client_id, project_id, title, description,
  priority, status, assigned_to, created_by,
  created_at, updated_at, deleted_at
)

-- Ticket messages
ticket_messages (
  id, ticket_id, user_id, client_user_id, message,
  is_internal, created_at, updated_at, deleted_at
)

-- Ticket attachments
ticket_attachments (
  id, ticket_id, message_id, filename, file_url,
  file_size, created_at, updated_at
)
```

#### Client Notifications

```sql
-- Client notifications
client_notifications (
  id, client_user_id, type, title, message,
  is_read, read_at, created_at, updated_at
)
```

### 6. Third-party Integrations

```sql
-- Integration configurations
integrations (
  id, organization_id, provider, config_data,
  is_active, created_at, updated_at, deleted_at
)

-- Client integration access
client_integration_access (
  id, client_id, integration_id, access_level,
  granted_by, granted_at, expires_at,
  created_at, updated_at
)
```

### 7. Document Generation

```sql
-- Document templates
document_templates (
  id, organization_id, type, name, template_data,
  variables, is_active, created_by,
  created_at, updated_at, deleted_at
)

-- Generated documents
generated_documents (
  id, template_id, entity_type, entity_id,
  file_url, generated_by, generated_at,
  created_at, updated_at
)
```

### 8. Audit & Activity Tracking

```sql
-- Activity logs
activity_logs (
  id, organization_id, user_id, entity_type, entity_id,
  action, old_values, new_values, ip_address,
  user_agent, created_at
)

-- File uploads
file_uploads (
  id, organization_id, uploaded_by, filename,
  file_path, file_size, mime_type, entity_type,
  entity_id, created_at, updated_at, deleted_at
)
```

## Implementation Priority

### Phase 1: Core Enhancements (Week 1-2)

1. Add missing imports to schema.ts (boolean, numeric)
2. Enhance existing tables with missing fields
3. Add sprint management
4. Add custom task statuses
5. Add client contacts and preferences

### Phase 2: Financial & Contract Management (Week 3-4)

1. Receipt and expense tracking
2. Payment management
3. Contract system
4. Document generation

### Phase 3: Client Portal & Integrations (Week 5-6)

1. Client portal authentication
2. Support ticket system
3. Third-party integrations
4. Client notifications

### Phase 4: Advanced Features (Week 7-8)

1. Audit trails
2. File management system
3. Advanced reporting structures
4. Performance optimizations

## Database Indexes Strategy

### Primary Indexes (Performance Critical)

- `organizations.id` (Primary Key)
- `projects.organization_id` (Multi-tenant isolation)
- `clients.organization_id` (Multi-tenant isolation)
- `invoices.client_id, status` (Financial queries)
- `project_tasks.project_id, status_id` (Task management)

### Secondary Indexes (Query Optimization)

- `activity_logs.created_at` (Audit queries)
- `support_tickets.status, priority` (Support dashboard)
- `contracts.status, expires_date` (Contract management)
- `payment_reminders.sent_at, status` (Payment processing)

## Data Integrity Considerations

1. **Multi-tenancy**: All tables include `organization_id` for data isolation
2. **Soft Deletes**: `deleted_at` timestamp for data recovery
3. **Audit Trail**: Activity logs for all critical operations
4. **Foreign Key Constraints**: Proper cascading deletes
5. **Unique Constraints**: Prevent duplicate records where appropriate

## Technical Implementation Details

### Schema Modifications Required

#### 1. Fix Missing Imports in schema.ts

```typescript
import {
  pgTable,
  text,
  timestamp,
  varchar,
  boolean,
  numeric,
  integer,
} from "drizzle-orm/pg-core";
```

#### 2. Enhance Existing Tables

**Projects Table Enhancements:**

```typescript
export const projects = pgTable("projects", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  status: varchar("status", { length: 50 }).notNull().default("active"), // active, completed, on_hold, cancelled
  startDate: timestamp("start_date"),
  endDate: timestamp("end_date"),
  budget: numeric("budget", { precision: 10, scale: 2 }),
  currency: varchar("currency", { length: 3 }).default("USD"),
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
});
```

**Project Tasks Table Enhancements:**

```typescript
export const projectTasks = pgTable("project_tasks", {
  ...commonColumns,
  projectId: varchar("project_id")
    .notNull()
    .references(() => projects.id, { onDelete: "cascade" }),
  sprintId: varchar("sprint_id").references(() => projectSprints.id, {
    onDelete: "set null",
  }),
  parentTaskId: varchar("parent_task_id").references(() => projectTasks.id, {
    onDelete: "cascade",
  }),
  statusId: varchar("status_id").references(() => taskStatuses.id, {
    onDelete: "restrict",
  }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  priority: varchar("priority", { length: 20 }).default("medium"), // low, medium, high, urgent
  estimatedHours: numeric("estimated_hours", { precision: 5, scale: 2 }),
  actualHours: numeric("actual_hours", { precision: 5, scale: 2 }),
  dueDate: timestamp("due_date"),
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  order: integer("order").default(0),
});
```

**Clients Table Enhancements:**

```typescript
export const clients = pgTable("clients", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  companyName: varchar("company_name", { length: 255 }),
  email: varchar("email", { length: 255 }),
  phone: varchar("phone", { length: 50 }),
  website: varchar("website", { length: 255 }),
  description: text("description"),
  avatar: varchar("avatar", { length: 255 }),
  status: varchar("status", { length: 20 }).default("active"), // active, inactive, archived
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
});
```

**Invoices Table Enhancements:**

```typescript
export const invoices = pgTable("invoices", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  projectId: varchar("project_id").references(() => projects.id, {
    onDelete: "set null",
  }),
  number: varchar("number", { length: 255 }).notNull().unique(),
  title: varchar("title", { length: 255 }),
  date: timestamp("date").notNull(),
  dueDate: timestamp("due_date").notNull(),
  subtotal: numeric("subtotal", { precision: 10, scale: 2 }).notNull(),
  taxAmount: numeric("tax_amount", { precision: 10, scale: 2 }).default("0"),
  discountAmount: numeric("discount_amount", {
    precision: 10,
    scale: 2,
  }).default("0"),
  totalAmount: numeric("total_amount", { precision: 10, scale: 2 }).notNull(),
  currency: varchar("currency", { length: 3 }).default("USD"),
  status: varchar("status", { length: 20 }).notNull().default("draft"), // draft, sent, paid, overdue, cancelled
  notes: text("notes"),
  terms: text("terms"),
  paidAt: timestamp("paid_at"),
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
});
```

### New Tables Implementation

#### Sprint Management

```typescript
export const projectSprints = pgTable("project_sprints", {
  ...commonColumns,
  projectId: varchar("project_id")
    .notNull()
    .references(() => projects.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  status: varchar("status", { length: 20 }).default("planned"), // planned, active, completed, cancelled
  goal: text("goal"),
  order: integer("order").default(0),
});
```

#### Task Status Management

```typescript
export const taskStatuses = pgTable("task_statuses", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 100 }).notNull(),
  color: varchar("color", { length: 7 }).default("#6B7280"), // hex color
  order: integer("order").default(0),
  isDefault: boolean("is_default").default(false),
  isCompleted: boolean("is_completed").default(false), // marks task as completed
});
```

#### Client Contact Management

```typescript
export const clientContacts = pgTable("client_contacts", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  email: varchar("email", { length: 255 }),
  phone: varchar("phone", { length: 50 }),
  position: varchar("position", { length: 255 }),
  isPrimary: boolean("is_primary").default(false),
});

export const clientPreferences = pgTable("client_preferences", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  communicationMethod: varchar("communication_method", { length: 50 }).default(
    "email"
  ), // email, phone, slack
  timezone: varchar("timezone", { length: 100 }).default("UTC"),
  language: varchar("language", { length: 10 }).default("en"),
  notificationSettings: text("notification_settings"), // JSON string
});
```

#### Client Portal Authentication

```typescript
export const clientUsers = pgTable("client_users", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  email: varchar("email", { length: 255 }).notNull().unique(),
  passwordHash: varchar("password_hash", { length: 255 }),
  name: varchar("name", { length: 255 }).notNull(),
  role: varchar("role", { length: 50 }).default("viewer"), // viewer, collaborator, admin
  lastLogin: timestamp("last_login"),
  isActive: boolean("is_active").default(true),
  emailVerified: boolean("email_verified").default(false),
});

export const clientSessions = pgTable("client_sessions", {
  ...commonColumns,
  clientUserId: varchar("client_user_id")
    .notNull()
    .references(() => clientUsers.id, { onDelete: "cascade" }),
  token: varchar("token", { length: 255 }).notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  ipAddress: varchar("ip_address", { length: 255 }),
  userAgent: varchar("user_agent", { length: 500 }),
});
```

#### Financial Management Extensions

```typescript
export const receipts = pgTable("receipts", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  vendorName: varchar("vendor_name", { length: 255 }).notNull(),
  amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
  currency: varchar("currency", { length: 3 }).default("USD"),
  receiptDate: timestamp("receipt_date").notNull(),
  categoryId: varchar("category_id").references(() => expenseCategories.id, {
    onDelete: "set null",
  }),
  description: text("description"),
  fileUrl: varchar("file_url", { length: 500 }),
  createdBy: varchar("created_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const expenseCategories = pgTable("expense_categories", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  isActive: boolean("is_active").default(true),
});

export const invoicePayments = pgTable("invoice_payments", {
  ...commonColumns,
  invoiceId: varchar("invoice_id")
    .notNull()
    .references(() => invoices.id, { onDelete: "cascade" }),
  amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
  paymentDate: timestamp("payment_date").notNull(),
  paymentMethod: varchar("payment_method", { length: 50 }), // bank_transfer, credit_card, paypal, etc.
  transactionId: varchar("transaction_id", { length: 255 }),
  notes: text("notes"),
});

export const paymentReminders = pgTable("payment_reminders", {
  ...commonColumns,
  invoiceId: varchar("invoice_id")
    .notNull()
    .references(() => invoices.id, { onDelete: "cascade" }),
  reminderType: varchar("reminder_type", { length: 50 }).notNull(), // first, second, final, custom
  sentAt: timestamp("sent_at").notNull(),
  status: varchar("status", { length: 20 }).default("sent"), // sent, delivered, opened, failed
});

export const lateFees = pgTable("late_fees", {
  ...commonColumns,
  invoiceId: varchar("invoice_id")
    .notNull()
    .references(() => invoices.id, { onDelete: "cascade" }),
  feeAmount: numeric("fee_amount", { precision: 10, scale: 2 }).notNull(),
  appliedDate: timestamp("applied_date").notNull(),
  reason: text("reason"),
});
```

#### Contract Management

```typescript
export const contractTemplates = pgTable("contract_templates", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  content: text("content").notNull(), // HTML/Markdown template
  variables: text("variables"), // JSON array of template variables
  isActive: boolean("is_active").default(true),
  createdBy: varchar("created_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const contracts = pgTable("contracts", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  projectId: varchar("project_id").references(() => projects.id, {
    onDelete: "set null",
  }),
  templateId: varchar("template_id").references(() => contractTemplates.id, {
    onDelete: "set null",
  }),
  title: varchar("title", { length: 255 }).notNull(),
  content: text("content").notNull(),
  status: varchar("status", { length: 20 }).default("draft"), // draft, sent, signed, expired, cancelled
  signedDate: timestamp("signed_date"),
  expiresDate: timestamp("expires_date"),
  value: numeric("value", { precision: 10, scale: 2 }),
  currency: varchar("currency", { length: 3 }).default("USD"),
  createdBy: varchar("created_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const contractSignatures = pgTable("contract_signatures", {
  ...commonColumns,
  contractId: varchar("contract_id")
    .notNull()
    .references(() => contracts.id, { onDelete: "cascade" }),
  signerName: varchar("signer_name", { length: 255 }).notNull(),
  signerEmail: varchar("signer_email", { length: 255 }).notNull(),
  signatureData: text("signature_data"), // Base64 signature image or digital signature
  signedAt: timestamp("signed_at").notNull(),
  ipAddress: varchar("ip_address", { length: 255 }),
});
```

#### Support Ticket System

```typescript
export const supportTickets = pgTable("support_tickets", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  projectId: varchar("project_id").references(() => projects.id, {
    onDelete: "set null",
  }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description").notNull(),
  priority: varchar("priority", { length: 20 }).default("medium"), // low, medium, high, urgent
  status: varchar("status", { length: 20 }).default("open"), // open, in_progress, resolved, closed
  assignedTo: varchar("assigned_to").references(() => users.id, {
    onDelete: "set null",
  }),
  createdBy: varchar("created_by").references(() => clientUsers.id, {
    onDelete: "cascade",
  }),
  resolvedAt: timestamp("resolved_at"),
  closedAt: timestamp("closed_at"),
});

export const ticketMessages = pgTable("ticket_messages", {
  ...commonColumns,
  ticketId: varchar("ticket_id")
    .notNull()
    .references(() => supportTickets.id, { onDelete: "cascade" }),
  userId: varchar("user_id").references(() => users.id, {
    onDelete: "cascade",
  }),
  clientUserId: varchar("client_user_id").references(() => clientUsers.id, {
    onDelete: "cascade",
  }),
  message: text("message").notNull(),
  isInternal: boolean("is_internal").default(false), // internal notes vs client-visible
});

export const ticketAttachments = pgTable("ticket_attachments", {
  ...commonColumns,
  ticketId: varchar("ticket_id")
    .notNull()
    .references(() => supportTickets.id, { onDelete: "cascade" }),
  messageId: varchar("message_id").references(() => ticketMessages.id, {
    onDelete: "cascade",
  }),
  filename: varchar("filename", { length: 255 }).notNull(),
  fileUrl: varchar("file_url", { length: 500 }).notNull(),
  fileSize: integer("file_size"),
  mimeType: varchar("mime_type", { length: 100 }),
});
```

#### Third-party Integrations

```typescript
export const integrations = pgTable("integrations", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  provider: varchar("provider", { length: 50 }).notNull(), // slack, google_drive, github, etc.
  name: varchar("name", { length: 255 }).notNull(),
  configData: text("config_data"), // JSON configuration
  isActive: boolean("is_active").default(true),
  lastSyncAt: timestamp("last_sync_at"),
});

export const clientIntegrationAccess = pgTable("client_integration_access", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  integrationId: varchar("integration_id")
    .notNull()
    .references(() => integrations.id, { onDelete: "cascade" }),
  accessLevel: varchar("access_level", { length: 20 }).default("read"), // read, write, admin
  grantedBy: varchar("granted_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  grantedAt: timestamp("granted_at").notNull(),
  expiresAt: timestamp("expires_at"),
});
```

#### Document Generation & File Management

```typescript
export const documentTemplates = pgTable("document_templates", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  type: varchar("type", { length: 50 }).notNull(), // invoice, proposal, contract, eta
  name: varchar("name", { length: 255 }).notNull(),
  templateData: text("template_data").notNull(), // HTML/JSON template
  variables: text("variables"), // JSON array of available variables
  isActive: boolean("is_active").default(true),
  createdBy: varchar("created_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const generatedDocuments = pgTable("generated_documents", {
  ...commonColumns,
  templateId: varchar("template_id").references(() => documentTemplates.id, {
    onDelete: "set null",
  }),
  entityType: varchar("entity_type", { length: 50 }).notNull(), // project, invoice, contract
  entityId: varchar("entity_id", { length: 255 }).notNull(),
  filename: varchar("filename", { length: 255 }).notNull(),
  fileUrl: varchar("file_url", { length: 500 }).notNull(),
  fileSize: integer("file_size"),
  generatedBy: varchar("generated_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  generatedAt: timestamp("generated_at").notNull(),
});

export const fileUploads = pgTable("file_uploads", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  uploadedBy: varchar("uploaded_by")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  filename: varchar("filename", { length: 255 }).notNull(),
  originalName: varchar("original_name", { length: 255 }).notNull(),
  filePath: varchar("file_path", { length: 500 }).notNull(),
  fileSize: integer("file_size").notNull(),
  mimeType: varchar("mime_type", { length: 100 }).notNull(),
  entityType: varchar("entity_type", { length: 50 }), // project, task, ticket, etc.
  entityId: varchar("entity_id", { length: 255 }),
});
```

#### Audit Trail & Activity Tracking

```typescript
export const activityLogs = pgTable("activity_logs", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  userId: varchar("user_id").references(() => users.id, {
    onDelete: "set null",
  }),
  entityType: varchar("entity_type", { length: 50 }).notNull(),
  entityId: varchar("entity_id", { length: 255 }).notNull(),
  action: varchar("action", { length: 50 }).notNull(), // create, update, delete, etc.
  oldValues: text("old_values"), // JSON of previous values
  newValues: text("new_values"), // JSON of new values
  ipAddress: varchar("ip_address", { length: 255 }),
  userAgent: varchar("user_agent", { length: 500 }),
});

export const clientNotifications = pgTable("client_notifications", {
  ...commonColumns,
  clientUserId: varchar("client_user_id")
    .notNull()
    .references(() => clientUsers.id, { onDelete: "cascade" }),
  type: varchar("type", { length: 50 }).notNull(), // project_update, invoice_sent, etc.
  title: varchar("title", { length: 255 }).notNull(),
  message: text("message").notNull(),
  isRead: boolean("is_read").default(false),
  readAt: timestamp("read_at"),
  actionUrl: varchar("action_url", { length: 500 }),
});
```

### Database Relationships & Constraints

#### Composite Indexes for Performance

```sql
-- Multi-tenant data isolation
CREATE INDEX idx_projects_org_client ON projects(organization_id, client_id);
CREATE INDEX idx_tasks_project_status ON project_tasks(project_id, status_id);
CREATE INDEX idx_invoices_client_status ON invoices(client_id, status);

-- Time-based queries
CREATE INDEX idx_tasks_due_date ON project_tasks(due_date) WHERE due_date IS NOT NULL;
CREATE INDEX idx_invoices_due_date ON invoices(due_date, status);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- Search optimization
CREATE INDEX idx_clients_name_search ON clients USING gin(to_tsvector('english', name || ' ' || COALESCE(company_name, '')));
CREATE INDEX idx_projects_name_search ON projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
```

#### Data Validation Rules

```sql
-- Ensure invoice totals are calculated correctly
ALTER TABLE invoices ADD CONSTRAINT check_invoice_total
  CHECK (total_amount = subtotal + tax_amount - discount_amount);

-- Ensure task dates are logical
ALTER TABLE project_tasks ADD CONSTRAINT check_task_dates
  CHECK (started_at IS NULL OR completed_at IS NULL OR started_at <= completed_at);

-- Ensure sprint dates are logical
ALTER TABLE project_sprints ADD CONSTRAINT check_sprint_dates
  CHECK (start_date <= end_date);
```

## Migration Strategy

### Phase 1 Migration Script

```sql
-- Add missing columns to existing tables
ALTER TABLE projects ADD COLUMN status VARCHAR(50) DEFAULT 'active';
ALTER TABLE projects ADD COLUMN start_date TIMESTAMP;
ALTER TABLE projects ADD COLUMN end_date TIMESTAMP;
ALTER TABLE projects ADD COLUMN budget NUMERIC(10,2);
ALTER TABLE projects ADD COLUMN currency VARCHAR(3) DEFAULT 'USD';

-- Create new tables
CREATE TABLE project_sprints (...);
CREATE TABLE task_statuses (...);

-- Create default task statuses for existing organizations
INSERT INTO task_statuses (organization_id, name, color, "order", is_default)
SELECT id, 'To Do', '#6B7280', 1, true FROM organizations
UNION ALL
SELECT id, 'In Progress', '#3B82F6', 2, false FROM organizations
UNION ALL
SELECT id, 'Done', '#10B981', 3, false FROM organizations;
```

## Next Steps

1. **Review and approve this plan**
2. **Implement Phase 1 schema changes**
   - Fix missing imports in schema.ts
   - Add enhanced table definitions
   - Create migration scripts
3. **Update TypeScript types and relations**
4. **Implement corresponding API endpoints**
5. **Create database seeders for testing**
6. **Set up proper indexing strategy**
7. **Implement data validation rules**
8. **Create backup and rollback procedures**
